// Final comprehensive test and fix for MCP server
const axios = require('axios');
const admin = require('firebase-admin');

// CRITICAL: Set emulator environment variables BEFORE initializing Firebase
process.env.FIRESTORE_EMULATOR_HOST = 'localhost:8080';
process.env.FIREBASE_AUTH_EMULATOR_HOST = 'localhost:9099';

// Initialize Firebase Admin for emulator
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: 'kissandost-9570f'
  });
}

const db = admin.firestore();

// Your actual data
const FARM_ID = 'KWct4gdzPvEJfS4XP35C';
const USER_ID = 'rir9S0uQlhRVp9cmhLJZXT54Atc2';
const MCP_URL = 'http://localhost:5001/kissandost-9570f/us-central1/mcp';

async function seedEmulatorData() {
  console.log('🌱 Seeding Firebase Emulator (FINAL)...\n');
  
  try {
    // Clear existing data first
    console.log('🧹 Clearing existing data...');
    
    // Delete existing collections
    const collections = ['animals', 'plants', 'users', 'zones', 'tasks'];
    for (const collection of collections) {
      const snapshot = await db.collection(`farms/${FARM_ID}/${collection}`).get();
      const batch = db.batch();
      snapshot.docs.forEach(doc => batch.delete(doc.ref));
      if (!snapshot.empty) {
        await batch.commit();
      }
    }
    
    // Delete lookups
    const lookupsSnapshot = await db.collection('lookups').get();
    const lookupsBatch = db.batch();
    lookupsSnapshot.docs.forEach(doc => lookupsBatch.delete(doc.ref));
    if (!lookupsSnapshot.empty) {
      await lookupsBatch.commit();
    }
    
    console.log('✅ Existing data cleared');

    // Add farm
    console.log('📋 Creating farm...');
    await db.collection('farms').doc(FARM_ID).set({
      name: 'Test Farm',
      owner: USER_ID,
      location: 'Test Location',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Add users
    console.log('👥 Creating users...');
    await db.collection('farms').doc(FARM_ID).collection('users').doc(USER_ID).set({
      name: 'Test Admin',
      role: 'admin',
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    await db.collection('farms').doc(FARM_ID).collection('users').doc('user_adnan').set({
      name: 'Adnan',
      role: 'worker',
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Add zones
    console.log('🏞️ Creating zones...');
    await db.collection('farms').doc(FARM_ID).collection('zones').doc('zone_3').set({
      name: 'Zone 3',
      type: 'field',
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Add animals
    console.log('🐄 Creating animals...');
    await db.collection('farms').doc(FARM_ID).collection('animals').doc('animal_1').set({
      tagId: 'BUFF-001',
      name: 'Bella',
      typeId: 'buffalo',
      status: 'active',
      healthStatus: 'healthy',
      zoneId: 'zone_3',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    await db.collection('farms').doc(FARM_ID).collection('animals').doc('animal_2').set({
      tagId: 'COW-001',
      name: 'Moti',
      typeId: 'cow',
      status: 'active',
      healthStatus: 'healthy',
      zoneId: 'zone_3',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Add plants
    console.log('🌱 Creating plants...');
    await db.collection('farms').doc(FARM_ID).collection('plants').doc('plant_1').set({
      name: 'Tomato Crop',
      varietyId: 'tomato',
      status: 'growing',
      healthStatus: 'healthy',
      zoneId: 'zone_3',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    await db.collection('farms').doc(FARM_ID).collection('plants').doc('plant_2').set({
      name: 'Wheat Field',
      varietyId: 'wheat',
      status: 'growing',
      healthStatus: 'healthy',
      zoneId: 'zone_3',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Add lookups
    console.log('📚 Creating lookups...');
    const lookups = [
      { id: 'buffalo', title: 'Buffalo', categoryName: 'Animal Type', type: 'animal' },
      { id: 'cow', title: 'Cow', categoryName: 'Animal Type', type: 'animal' },
      { id: 'tomato', title: 'Tomato', categoryName: 'Plant Variety', type: 'plant' },
      { id: 'wheat', title: 'Wheat', categoryName: 'Plant Variety', type: 'plant' }
    ];

    for (const lookup of lookups) {
      await db.collection('lookups').doc(lookup.id).set({
        title: lookup.title,
        categoryName: lookup.categoryName,
        type: lookup.type,
        parentId: null,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
    }

    console.log('✅ Emulator data seeded successfully!');
    
    // Verify data
    const animalsSnapshot = await db.collection(`farms/${FARM_ID}/animals`).get();
    const plantsSnapshot = await db.collection(`farms/${FARM_ID}/plants`).get();
    const usersSnapshot = await db.collection(`farms/${FARM_ID}/users`).get();
    const zonesSnapshot = await db.collection(`farms/${FARM_ID}/zones`).get();
    
    console.log('\n📊 Verification:');
    console.log(`- Animals: ${animalsSnapshot.size}`);
    console.log(`- Plants: ${plantsSnapshot.size}`);
    console.log(`- Users: ${usersSnapshot.size}`);
    console.log(`- Zones: ${zonesSnapshot.size}`);
    
    if (animalsSnapshot.size > 0) {
      console.log('\n🐄 Sample Animal:');
      console.log(animalsSnapshot.docs[0].data());
    }
    
    if (plantsSnapshot.size > 0) {
      console.log('\n🌱 Sample Plant:');
      console.log(plantsSnapshot.docs[0].data());
    }
    
  } catch (error) {
    console.error('❌ Error seeding emulator data:', error);
    throw error;
  }
}

async function testMCPEndpoint() {
  console.log('\n🧪 Testing MCP Endpoint (FINAL)...\n');
  
  const tests = [
    {
      name: 'List Animals',
      request: {
        text: 'Show all animals',
        userId: USER_ID,
        farmId: FARM_ID
      }
    },
    {
      name: 'List Plants',
      request: {
        text: 'Show all plants',
        userId: USER_ID,
        farmId: FARM_ID
      }
    },
    {
      name: 'Assign Task (Fixed Date)',
      request: {
        text: 'Assign watering task to Adnan for 2025-08-01',
        userId: USER_ID,
        farmId: FARM_ID
      }
    }
  ];

  for (const test of tests) {
    try {
      console.log(`📋 Testing: ${test.name}`);
      console.log(`📤 Request: "${test.request.text}"`);
      
      const response = await axios.post(MCP_URL, test.request, {
        headers: { 'Content-Type': 'application/json' },
        timeout: 15000
      });
      
      console.log(`✅ Status: ${response.data.status}`);
      console.log(`💬 Message: ${response.data.message}`);
      
      if (response.data.data) {
        if (Array.isArray(response.data.data)) {
          console.log(`📊 Found ${response.data.data.length} items`);
          response.data.data.forEach((item, index) => {
            console.log(`  ${index + 1}. ${item.name || item.title || item.tagId || 'Unknown'}`);
          });
        } else {
          console.log(`📊 Data:`, response.data.data);
        }
      }
      
      if (response.data.meta) {
        console.log(`📋 Meta:`, response.data.meta);
      }
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      if (error.response) {
        console.log(`📄 Response:`, error.response.data);
      }
    }
    
    console.log('---\n');
  }
}

async function runFinalTest() {
  console.log('🚀 FINAL MCP TEST AND FIX\n');
  console.log('Environment:');
  console.log(`- FIRESTORE_EMULATOR_HOST: ${process.env.FIRESTORE_EMULATOR_HOST}`);
  console.log(`- Project ID: kissandost-9570f`);
  console.log(`- Farm ID: ${FARM_ID}`);
  console.log(`- User ID: ${USER_ID}`);
  console.log('');
  
  await seedEmulatorData();
  
  // Wait a moment for data to be available
  console.log('⏳ Waiting 2 seconds for data to be available...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  await testMCPEndpoint();
  
  console.log('🎉 FINAL TEST COMPLETED!');
  console.log('\n📋 Summary:');
  console.log('1. ✅ Data seeded to emulator');
  console.log('2. ✅ MCP endpoint tested');
  console.log('3. 🔧 If animals/plants still show 0, the MCP server needs restart');
  console.log('4. 🔧 If dates still fail, the LLM prompt needs stronger enforcement');
}

runFinalTest().catch(console.error);
