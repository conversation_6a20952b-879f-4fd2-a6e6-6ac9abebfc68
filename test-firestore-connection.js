// Comprehensive Firestore connection and data test
const admin = require('firebase-admin');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: 'demo-project' // Change this to your actual project ID
  });
}

const db = admin.firestore();

// Your actual data
const FARM_ID = 'KWct4gdzPvEJfS4XP35C';
const USER_ID = 'rir9S0uQlhRVp9cmhLJZXT54Atc2';

async function testFirestoreConnection() {
  console.log('🔥 Testing Firestore Connection...\n');
  
  try {
    // Test 1: Basic connection test
    console.log('1️⃣ Testing basic Firestore connection...');
    const testDoc = await db.collection('test').doc('connection').set({
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      message: 'Connection test'
    });
    console.log('✅ Basic Firestore connection works');
    
    // Clean up test document
    await db.collection('test').doc('connection').delete();
    console.log('');

    // Test 2: Check if your farm exists
    console.log('2️⃣ Checking if your farm exists...');
    const farmDoc = await db.collection('farms').doc(FARM_ID).get();
    if (farmDoc.exists) {
      console.log('✅ Farm exists:', farmDoc.data());
    } else {
      console.log('❌ Farm does not exist - creating it...');
      await db.collection('farms').doc(FARM_ID).set({
        name: 'Test Farm',
        owner: USER_ID,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      console.log('✅ Farm created');
    }
    console.log('');

    // Test 3: Test animals collection
    console.log('3️⃣ Testing animals collection...');
    const animalsPath = `farms/${FARM_ID}/animals`;
    console.log(`Collection path: ${animalsPath}`);
    
    // Try to read animals
    const animalsSnapshot = await db.collection(animalsPath).get();
    console.log(`Found ${animalsSnapshot.size} animals`);
    
    if (animalsSnapshot.empty) {
      console.log('📝 Adding test animal...');
      await db.collection(animalsPath).doc('test_animal').set({
        tagId: 'TEST-001',
        name: 'Test Animal',
        typeId: 'cow',
        status: 'active',
        healthStatus: 'healthy',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      console.log('✅ Test animal added');
      
      // Read again to confirm
      const animalsSnapshot2 = await db.collection(animalsPath).get();
      console.log(`Now found ${animalsSnapshot2.size} animals`);
    } else {
      console.log('✅ Animals collection has data:');
      animalsSnapshot.forEach(doc => {
        console.log(`  - ${doc.id}:`, doc.data());
      });
    }
    console.log('');

    // Test 4: Test plants collection
    console.log('4️⃣ Testing plants collection...');
    const plantsPath = `farms/${FARM_ID}/plants`;
    console.log(`Collection path: ${plantsPath}`);
    
    const plantsSnapshot = await db.collection(plantsPath).get();
    console.log(`Found ${plantsSnapshot.size} plants`);
    
    if (plantsSnapshot.empty) {
      console.log('📝 Adding test plant...');
      await db.collection(plantsPath).doc('test_plant').set({
        name: 'Test Plant',
        varietyId: 'tomato',
        status: 'growing',
        healthStatus: 'healthy',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      console.log('✅ Test plant added');
      
      // Read again to confirm
      const plantsSnapshot2 = await db.collection(plantsPath).get();
      console.log(`Now found ${plantsSnapshot2.size} plants`);
    } else {
      console.log('✅ Plants collection has data:');
      plantsSnapshot.forEach(doc => {
        console.log(`  - ${doc.id}:`, doc.data());
      });
    }
    console.log('');

    // Test 5: Test users collection
    console.log('5️⃣ Testing users collection...');
    const usersPath = `farms/${FARM_ID}/users`;
    console.log(`Collection path: ${usersPath}`);
    
    const usersSnapshot = await db.collection(usersPath).get();
    console.log(`Found ${usersSnapshot.size} users`);
    
    // Check if your user exists
    const userDoc = await db.collection(usersPath).doc(USER_ID).get();
    if (userDoc.exists) {
      console.log('✅ Your user exists:', userDoc.data());
    } else {
      console.log('❌ Your user does not exist - creating it...');
      await db.collection(usersPath).doc(USER_ID).set({
        name: 'Test Admin',
        role: 'admin',
        status: 'active',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      console.log('✅ User created');
    }
    
    // Add Adnan user for testing
    const adnanDoc = await db.collection(usersPath).doc('user_adnan').get();
    if (!adnanDoc.exists) {
      console.log('📝 Adding Adnan user...');
      await db.collection(usersPath).doc('user_adnan').set({
        name: 'Adnan',
        role: 'worker',
        status: 'active',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      console.log('✅ Adnan user added');
    }
    console.log('');

    // Test 6: Test zones collection
    console.log('6️⃣ Testing zones collection...');
    const zonesPath = `farms/${FARM_ID}/zones`;
    console.log(`Collection path: ${zonesPath}`);
    
    const zonesSnapshot = await db.collection(zonesPath).get();
    console.log(`Found ${zonesSnapshot.size} zones`);
    
    // Add Zone 3 if it doesn't exist
    const zone3Query = await db.collection(zonesPath).where('name', '==', 'Zone 3').get();
    if (zone3Query.empty) {
      console.log('📝 Adding Zone 3...');
      await db.collection(zonesPath).doc('zone_3').set({
        name: 'Zone 3',
        type: 'field',
        status: 'active',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      console.log('✅ Zone 3 added');
    } else {
      console.log('✅ Zone 3 exists');
    }
    console.log('');

    // Test 7: Test MCP firestoreService
    console.log('7️⃣ Testing MCP firestoreService...');
    const firestoreService = require('./functions/mcp/services/firestoreService');
    
    console.log('Testing getDocuments method...');
    const animals = await firestoreService.getDocuments(animalsPath);
    console.log(`✅ firestoreService.getDocuments returned ${animals.length} animals`);
    
    const plants = await firestoreService.getDocuments(plantsPath);
    console.log(`✅ firestoreService.getDocuments returned ${plants.length} plants`);
    
    const users = await firestoreService.getDocuments(usersPath);
    console.log(`✅ firestoreService.getDocuments returned ${users.length} users`);
    console.log('');

    // Test 8: Test task creation
    console.log('8️⃣ Testing task creation...');
    const tasksPath = `farms/${FARM_ID}/tasks`;
    
    const testTask = {
      title: 'Test Task',
      description: 'This is a test task',
      assignedTo: 'user_adnan',
      assignedBy: USER_ID,
      status: 'pending',
      priority: 'medium',
      dueDate: new Date().toISOString().split('T')[0], // Today's date
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    };
    
    const taskDoc = await db.collection(tasksPath).add(testTask);
    console.log(`✅ Test task created with ID: ${taskDoc.id}`);
    
    // Read it back
    const createdTask = await taskDoc.get();
    console.log('✅ Task data:', createdTask.data());
    console.log('');

    console.log('🎉 All Firestore tests passed!');
    
  } catch (error) {
    console.error('❌ Firestore test failed:', error);
    console.error('Stack:', error.stack);
  }
}

// Test MCP components with real data
async function testMCPComponents() {
  console.log('\n🧪 Testing MCP Components...\n');
  
  // Set environment variables
  process.env.OPENROUTER_API_KEY = '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';
  
  try {
    // Test 1: Test listEntities controller directly
    console.log('1️⃣ Testing listEntities controller...');
    const listEntitiesController = require('./functions/mcp/controllers/listEntities');
    
    const intentData = {
      intent: 'listEntities',
      entity: 'animals',
      filters: {}
    };
    
    const context = {
      userId: USER_ID,
      farmId: FARM_ID
    };
    
    console.log('Intent data:', intentData);
    console.log('Context:', context);
    
    const result = await listEntitiesController.handle(intentData, context);
    console.log('✅ listEntities result:', JSON.stringify(result, null, 2));
    console.log('');

    // Test 2: Test assignTask controller directly
    console.log('2️⃣ Testing assignTask controller...');
    const assignTaskController = require('./functions/mcp/controllers/assignTask');
    
    const taskIntentData = {
      intent: 'assignTask',
      title: 'Test Watering Task',
      description: 'Water the plants',
      assignedTo: 'Adnan',
      dueDate: new Date().toISOString().split('T')[0] // Today
    };
    
    console.log('Task intent data:', taskIntentData);
    
    const taskResult = await assignTaskController.handle(taskIntentData, context);
    console.log('✅ assignTask result:', JSON.stringify(taskResult, null, 2));
    console.log('');

    // Test 3: Test full router flow
    console.log('3️⃣ Testing full router flow...');
    const router = require('./functions/mcp/router');
    
    const request = {
      text: 'Show all animals',
      userId: USER_ID,
      farmId: FARM_ID
    };
    
    console.log('Router request:', request);
    
    const routerResult = await router.processRequest(request);
    console.log('✅ Router result:', JSON.stringify(routerResult, null, 2));

  } catch (error) {
    console.error('❌ MCP component test failed:', error);
    console.error('Stack:', error.stack);
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting comprehensive Firestore and MCP tests...\n');
  
  await testFirestoreConnection();
  await testMCPComponents();
  
  console.log('\n🏁 All tests completed!');
  process.exit(0);
}

runAllTests().catch(console.error);
