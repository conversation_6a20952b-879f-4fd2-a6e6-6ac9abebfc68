/**
 * Prompt building utilities for OpenRouter LLM communication
 * Creates structured prompts for intent parsing and confirmations
 */
class BuildPrompt {
  /**
   * Create intent parsing prompt for user input
   * @param {string} text - User input text
   * @param {object} context - User and farm context
   * @returns {object} System and user prompts
   */
  static createIntentPrompt(text, context) {
    const systemPrompt = `You are an AI assistant for a farm management system called Kissan Dost. Your job is to parse natural language requests from farm owners, admins, and caretakers into structured JSON intents.

Available intents and their structures:

1. listEntities - List farm entities (animals, plants, zones, machinery, inventory)
{
  "intent": "listEntities",
  "entity": "animals|plants|zones|machinery|inventory",
  "filters": {
    "tagId": "string (for animals)",
    "type": "string",
    "status": "string",
    "zoneId": "string",
    "healthStatus": "string",
    "recentHealthCheck": boolean,
    "dateRange": {"start": "YYYY-MM-DD", "end": "YYYY-MM-DD"}
  }
}

2. assignTask - Assign a task to someone
{
  "intent": "assignTask",
  "title": "string",
  "description": "string",
  "assignedTo": "string (name or ID)",
  "zoneId": "string (optional)",
  "entityId": "string (optional)",
  "entityType": "animals|plants|machinery (optional)",
  "priority": "low|medium|high",
  "dueDate": "YYYY-MM-DD (optional)"
}

3. completeTask - Mark a task as complete
{
  "intent": "completeTask",
  "taskId": "string (optional)",
  "title": "string (optional, if no taskId)",
  "notes": "string (optional)"
}

4. createRecord - Create sub-entity records (health checks, feeding logs, etc.)
{
  "intent": "createRecord",
  "entityType": "animals|plants",
  "entityId": "string (optional)",
  "tagId": "string (for animals, optional)",
  "recordType": "healthCheck|pregnancy|feedingLog|harvestRecord",
  "data": {
    "condition": "string",
    "weight": "number",
    "temperature": "number",
    "notes": "string",
    "date": "YYYY-MM-DD"
  }
}

Rules:
- Always respond with valid JSON only
- If the request is unclear, use the most likely intent
- For dates, use YYYY-MM-DD format
- CRITICAL: Current year is 2025, today is 2025-07-30
- NEVER use dates from 2024 or earlier - ALL dates must be 2025 or later
- "today" = 2025-07-30
- "tomorrow" = 2025-07-31
- "next week" = 2025-08-06
- "next month" = 2025-08-30
- If no specific date is mentioned, use 2025-07-30
- Extract names, zones, and specific details from the text
- If information is missing, include what you can determine
- DOUBLE CHECK: All dueDate values must start with "2025-" or later

    const userPrompt = `Parse this farm management request into a JSON intent:

User: "${text}"
Context: User ID: ${context.userId}, Farm ID: ${context.farmId}

Respond with JSON only:`;

    return {
      systemPrompt,
      userPrompt
    };
  }

  /**
   * Create confirmation prompt for actions
   * @param {object} actionData - Action details
   * @returns {object} System and user prompts
   */
  static createConfirmationPrompt(actionData) {
    const systemPrompt = `You are an AI assistant for a farm management system. Generate a friendly, natural confirmation message for completed actions. Keep it concise and informative.

Guidelines:
- Use simple, clear language
- Include relevant details like names, zones, dates
- Be positive and encouraging
- Keep it under 50 words
- Don't use technical jargon`;

    let userPrompt = '';

    switch (actionData.intent) {
      case 'assignTask':
        userPrompt = `Generate a confirmation message for assigning a task:
Task: ${actionData.title}
Assigned to: ${actionData.assignedTo}
Zone: ${actionData.zone || 'Not specified'}
Due date: ${actionData.dueDate || 'Not specified'}`;
        break;

      case 'completeTask':
        userPrompt = `Generate a confirmation message for completing a task:
Task: ${actionData.title}
Completed by: ${actionData.completedBy}
Notes: ${actionData.notes || 'None'}`;
        break;

      case 'createRecord':
        userPrompt = `Generate a confirmation message for creating a record:
Record type: ${actionData.recordType}
Entity: ${actionData.entityName || actionData.tagId}
Details: ${JSON.stringify(actionData.data)}`;
        break;

      case 'listEntities':
        userPrompt = `Generate a summary message for listing entities:
Entity type: ${actionData.entity}
Count: ${actionData.count}
Filters applied: ${JSON.stringify(actionData.filters)}`;
        break;

      default:
        userPrompt = `Generate a confirmation message for: ${JSON.stringify(actionData)}`;
    }

    return {
      systemPrompt,
      userPrompt
    };
  }

  /**
   * Create context-aware prompt with farm data
   * @param {string} basePrompt - Base prompt text
   * @param {object} farmContext - Farm-specific context
   * @returns {string} Enhanced prompt
   */
  static enhanceWithContext(basePrompt, farmContext) {
    let contextInfo = '';

    if (farmContext.zones && farmContext.zones.length > 0) {
      contextInfo += `\nAvailable zones: ${farmContext.zones.map(z => z.name).join(', ')}`;
    }

    if (farmContext.users && farmContext.users.length > 0) {
      contextInfo += `\nFarm users: ${farmContext.users.map(u => u.name).join(', ')}`;
    }

    if (farmContext.animalTypes && farmContext.animalTypes.length > 0) {
      contextInfo += `\nAnimal types: ${farmContext.animalTypes.map(t => t.title).join(', ')}`;
    }

    return basePrompt + contextInfo;
  }

  /**
   * Create error handling prompt
   * @param {string} error - Error message
   * @param {string} originalText - Original user input
   * @returns {object} Error handling prompts
   */
  static createErrorPrompt(error, originalText) {
    const systemPrompt = `You are an AI assistant for a farm management system. The user's request could not be processed due to an error. Generate a helpful, friendly response that:

1. Acknowledges the issue
2. Suggests what might have gone wrong
3. Provides guidance on how to rephrase the request
4. Remains encouraging and supportive

Keep the response conversational and under 100 words.`;

    const userPrompt = `The user said: "${originalText}"
Error encountered: ${error}

Generate a helpful response:`;

    return {
      systemPrompt,
      userPrompt
    };
  }

  /**
   * Create multilingual prompt (future enhancement)
   * @param {string} text - User input
   * @param {string} language - Target language
   * @returns {object} Multilingual prompts
   */
  static createMultilingualPrompt(text, language = 'en') {
    // Placeholder for future multilingual support
    const languageInstructions = {
      'ur': 'Respond in Urdu language',
      'hi': 'Respond in Hindi language',
      'pa': 'Respond in Punjabi language',
      'en': 'Respond in English language'
    };

    const instruction = languageInstructions[language] || languageInstructions['en'];

    return {
      systemPrompt: `You are a multilingual farm management assistant. ${instruction}. Parse the following request and respond appropriately.`,
      userPrompt: text
    };
  }
}

module.exports = BuildPrompt;
