const firestoreService = require('../services/firestoreService');
const TaskModel = require('../models/taskModel');
const buildResponse = require('../utils/buildResponse');

/**
 * Controller for completing tasks
 * Handles marking tasks as complete with validation
 */
class CompleteTaskController {
  /**
   * Handle task completion request
   * @param {object} intentData - Parsed intent data from LLM
   * @param {object} context - User and farm context
   * @returns {object} Response with task completion result
   */
  async handle(intentData, context) {
    try {
      const { userId, farmId } = context;
      
      // Find the task to complete
      const task = await this.findTask(intentData, farmId);
      if (!task) {
        return buildResponse.notFound('Task');
      }

      // Check if user can complete this task
      const canComplete = await this.canCompleteTask(task, userId, farmId);
      if (!canComplete) {
        return buildResponse.error('You are not authorized to complete this task');
      }

      // Check if task is already completed
      if (task.status === 'completed') {
        return buildResponse.warning('Task is already completed', {
          taskId: task.id,
          title: task.title,
          completedAt: task.completedAt,
          completedBy: task.completedBy
        });
      }

      // Update task status to completed
      const updatedTask = TaskModel.updateStatus(task, 'completed', userId);
      
      // Add completion notes if provided
      if (intentData.notes) {
        updatedTask.notes = task.notes ? 
          `${task.notes}\n\nCompletion notes: ${intentData.notes}` : 
          `Completion notes: ${intentData.notes}`;
      }

      // Save updated task
      const collectionPath = TaskModel.getCollectionPath(farmId);
      await firestoreService.updateDocument(collectionPath, task.id, updatedTask);

      // Get user name for response
      const userName = await this.getUserName(userId, farmId);

      return buildResponse.taskResponse({
        ...updatedTask,
        id: task.id,
        completedByName: userName
      }, 'completed');

    } catch (error) {
      console.error('CompleteTask Error:', error);
      return buildResponse.error('Failed to complete task');
    }
  }

  /**
   * Find task by ID or title
   * @param {object} intentData - Intent data with task identifier
   * @param {string} farmId - Farm ID
   * @returns {object|null} Task object
   */
  async findTask(intentData, farmId) {
    try {
      const collectionPath = TaskModel.getCollectionPath(farmId);

      // Try to find by task ID first
      if (intentData.taskId) {
        const task = await firestoreService.getDocument(collectionPath, intentData.taskId);
        if (task) {
          return task;
        }
      }

      // Try to find by title
      if (intentData.title) {
        const tasks = await firestoreService.getDocuments(collectionPath);
        const taskByTitle = tasks.find(task => 
          task.title && task.title.toLowerCase().includes(intentData.title.toLowerCase())
        );
        
        if (taskByTitle) {
          return taskByTitle;
        }
      }

      return null;

    } catch (error) {
      console.error('Error finding task:', error);
      return null;
    }
  }

  /**
   * Check if user can complete the task
   * @param {object} task - Task object
   * @param {string} userId - User ID
   * @param {string} farmId - Farm ID
   * @returns {boolean} Can complete task
   */
  async canCompleteTask(task, userId, farmId) {
    try {
      // Task assignee can always complete their own tasks
      if (task.assignedTo === userId) {
        return true;
      }

      // Check if user is admin or owner
      const user = await firestoreService.getDocument(`farms/${farmId}/users`, userId);
      if (user && (user.role === 'admin' || user.role === 'owner')) {
        return true;
      }

      // Task creator can complete their assigned tasks
      if (task.assignedBy === userId) {
        return true;
      }

      return false;

    } catch (error) {
      console.error('Error checking task completion permissions:', error);
      return false;
    }
  }

  /**
   * Get user name for response
   * @param {string} userId - User ID
   * @param {string} farmId - Farm ID
   * @returns {string} User name
   */
  async getUserName(userId, farmId) {
    try {
      const user = await firestoreService.getDocument(`farms/${farmId}/users`, userId);
      return user ? user.name : userId;
    } catch (error) {
      console.error('Error getting user name:', error);
      return userId;
    }
  }

  /**
   * Get pending tasks for a user
   * @param {string} userId - User ID
   * @param {string} farmId - Farm ID
   * @returns {Array} Pending tasks
   */
  async getPendingTasks(userId, farmId) {
    try {
      const collectionPath = TaskModel.getCollectionPath(farmId);
      const filters = {
        assignedTo: userId,
        status: 'pending'
      };

      const tasks = await firestoreService.getDocuments(collectionPath, filters);
      
      // Sort by due date and priority
      return tasks.sort((a, b) => {
        // First sort by due date
        if (a.dueDate && b.dueDate) {
          const dateA = new Date(a.dueDate);
          const dateB = new Date(b.dueDate);
          if (dateA.getTime() !== dateB.getTime()) {
            return dateA.getTime() - dateB.getTime();
          }
        }
        
        // Then by priority
        const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
        return (priorityOrder[b.priority] || 2) - (priorityOrder[a.priority] || 2);
      });

    } catch (error) {
      console.error('Error getting pending tasks:', error);
      return [];
    }
  }

  /**
   * Get overdue tasks for a farm
   * @param {string} farmId - Farm ID
   * @returns {Array} Overdue tasks
   */
  async getOverdueTasks(farmId) {
    try {
      const collectionPath = TaskModel.getCollectionPath(farmId);
      const tasks = await firestoreService.getDocuments(collectionPath, {
        status: 'pending'
      });

      const now = new Date();
      const overdueTasks = tasks.filter(task => {
        if (!task.dueDate) return false;
        return new Date(task.dueDate) < now;
      });

      return overdueTasks.sort((a, b) => 
        new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()
      );

    } catch (error) {
      console.error('Error getting overdue tasks:', error);
      return [];
    }
  }

  /**
   * Mark multiple tasks as complete
   * @param {Array} taskIds - Array of task IDs
   * @param {string} userId - User ID
   * @param {string} farmId - Farm ID
   * @returns {object} Batch completion result
   */
  async completeMultipleTasks(taskIds, userId, farmId) {
    try {
      const results = [];
      const collectionPath = TaskModel.getCollectionPath(farmId);

      for (const taskId of taskIds) {
        try {
          const task = await firestoreService.getDocument(collectionPath, taskId);
          
          if (!task) {
            results.push({
              taskId: taskId,
              success: false,
              error: 'Task not found'
            });
            continue;
          }

          const canComplete = await this.canCompleteTask(task, userId, farmId);
          if (!canComplete) {
            results.push({
              taskId: taskId,
              success: false,
              error: 'Not authorized'
            });
            continue;
          }

          if (task.status === 'completed') {
            results.push({
              taskId: taskId,
              success: true,
              message: 'Already completed'
            });
            continue;
          }

          const updatedTask = TaskModel.updateStatus(task, 'completed', userId);
          await firestoreService.updateDocument(collectionPath, taskId, updatedTask);

          results.push({
            taskId: taskId,
            success: true,
            message: 'Completed successfully'
          });

        } catch (error) {
          results.push({
            taskId: taskId,
            success: false,
            error: error.message
          });
        }
      }

      return buildResponse.summary(results, 'Task completion');

    } catch (error) {
      console.error('Error completing multiple tasks:', error);
      return buildResponse.error('Failed to complete tasks');
    }
  }

  /**
   * Reopen a completed task
   * @param {string} taskId - Task ID
   * @param {string} userId - User ID
   * @param {string} farmId - Farm ID
   * @returns {object} Response
   */
  async reopenTask(taskId, userId, farmId) {
    try {
      const collectionPath = TaskModel.getCollectionPath(farmId);
      const task = await firestoreService.getDocument(collectionPath, taskId);

      if (!task) {
        return buildResponse.notFound('Task');
      }

      if (task.status !== 'completed') {
        return buildResponse.error('Task is not completed');
      }

      const canReopen = await this.canCompleteTask(task, userId, farmId);
      if (!canReopen) {
        return buildResponse.error('You are not authorized to reopen this task');
      }

      const updatedTask = TaskModel.updateStatus(task, 'pending', userId);
      updatedTask.completedAt = null;
      updatedTask.completedBy = null;

      await firestoreService.updateDocument(collectionPath, taskId, updatedTask);

      return buildResponse.taskResponse({
        ...updatedTask,
        id: taskId
      }, 'updated');

    } catch (error) {
      console.error('Error reopening task:', error);
      return buildResponse.error('Failed to reopen task');
    }
  }
}

module.exports = new CompleteTaskController();
