// Test Firebase configuration and project setup
const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

async function testFirebaseConfig() {
  console.log('🔧 Testing Firebase Configuration...\n');

  // Test 1: Check Firebase configuration files
  console.log('1️⃣ Checking Firebase configuration files...');
  
  const configFiles = [
    'firebase.json',
    'firestore.rules',
    'firestore.indexes.json'
  ];
  
  for (const file of configFiles) {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file} exists`);
      try {
        const content = fs.readFileSync(file, 'utf8');
        if (file === 'firebase.json') {
          const config = JSON.parse(content);
          console.log(`   Project config:`, config);
        }
      } catch (error) {
        console.log(`❌ Error reading ${file}:`, error.message);
      }
    } else {
      console.log(`❌ ${file} missing`);
    }
  }
  console.log('');

  // Test 2: Check environment variables
  console.log('2️⃣ Checking environment variables...');
  
  const envFiles = ['.env', 'functions/.env'];
  
  for (const envFile of envFiles) {
    if (fs.existsSync(envFile)) {
      console.log(`✅ ${envFile} exists`);
      try {
        const content = fs.readFileSync(envFile, 'utf8');
        const lines = content.split('\n').filter(line => line.trim() && !line.startsWith('#'));
        console.log(`   Variables: ${lines.length}`);
        lines.forEach(line => {
          const [key] = line.split('=');
          console.log(`   - ${key}`);
        });
      } catch (error) {
        console.log(`❌ Error reading ${envFile}:`, error.message);
      }
    } else {
      console.log(`❌ ${envFile} missing`);
    }
  }
  console.log('');

  // Test 3: Test Firebase Admin initialization
  console.log('3️⃣ Testing Firebase Admin initialization...');
  
  try {
    if (!admin.apps.length) {
      admin.initializeApp({
        projectId: 'kissandost-9570f'
      });
      console.log('✅ Firebase Admin initialized successfully');
    } else {
      console.log('✅ Firebase Admin already initialized');
    }
    
    const app = admin.app();
    console.log(`   Project ID: ${app.options.projectId}`);
    console.log(`   App name: ${app.name}`);
    
  } catch (error) {
    console.log('❌ Firebase Admin initialization failed:', error.message);
  }
  console.log('');

  // Test 4: Test Firestore connection
  console.log('4️⃣ Testing Firestore connection...');
  
  try {
    const db = admin.firestore();
    console.log('✅ Firestore instance created');
    
    // Test basic operation
    const testRef = db.collection('_test').doc('connection');
    await testRef.set({
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      test: true
    });
    console.log('✅ Test write successful');
    
    const testDoc = await testRef.get();
    if (testDoc.exists) {
      console.log('✅ Test read successful:', testDoc.data());
    } else {
      console.log('❌ Test document not found');
    }
    
    // Clean up
    await testRef.delete();
    console.log('✅ Test cleanup successful');
    
  } catch (error) {
    console.log('❌ Firestore connection failed:', error.message);
    console.log('   Stack:', error.stack);
  }
  console.log('');

  // Test 5: Check if running in emulator
  console.log('5️⃣ Checking emulator status...');
  
  const firestoreHost = process.env.FIRESTORE_EMULATOR_HOST;
  if (firestoreHost) {
    console.log(`✅ Running with Firestore emulator: ${firestoreHost}`);
  } else {
    console.log('ℹ️ Not using Firestore emulator (production mode)');
  }
  
  const functionsHost = process.env.FUNCTIONS_EMULATOR_HOST;
  if (functionsHost) {
    console.log(`✅ Running with Functions emulator: ${functionsHost}`);
  } else {
    console.log('ℹ️ Not using Functions emulator (production mode)');
  }
  console.log('');

  // Test 6: Test specific collections with your data
  console.log('6️⃣ Testing your specific data...');
  
  const FARM_ID = 'KWct4gdzPvEJfS4XP35C';
  const USER_ID = 'rir9S0uQlhRVp9cmhLJZXT54Atc2';
  
  try {
    const db = admin.firestore();
    
    // Test farm document
    console.log(`Checking farm: ${FARM_ID}`);
    const farmDoc = await db.collection('farms').doc(FARM_ID).get();
    if (farmDoc.exists) {
      console.log('✅ Farm exists:', farmDoc.data());
    } else {
      console.log('❌ Farm not found');
    }
    
    // Test collections
    const collections = ['animals', 'plants', 'users', 'zones', 'tasks'];
    
    for (const collection of collections) {
      const collectionPath = `farms/${FARM_ID}/${collection}`;
      console.log(`Checking collection: ${collectionPath}`);
      
      const snapshot = await db.collection(collectionPath).limit(5).get();
      console.log(`   Found ${snapshot.size} documents`);
      
      if (!snapshot.empty) {
        snapshot.forEach(doc => {
          console.log(`   - ${doc.id}: ${JSON.stringify(doc.data(), null, 2).substring(0, 100)}...`);
        });
      }
    }
    
    // Test lookups
    console.log('Checking lookups collection...');
    const lookupsSnapshot = await db.collection('lookups').limit(5).get();
    console.log(`   Found ${lookupsSnapshot.size} lookup documents`);
    
  } catch (error) {
    console.log('❌ Data check failed:', error.message);
  }
}

// Test MCP service integration
async function testMCPIntegration() {
  console.log('\n🔌 Testing MCP Service Integration...\n');
  
  try {
    // Test firestoreService
    console.log('1️⃣ Testing firestoreService...');
    const firestoreService = require('./functions/mcp/services/firestoreService');
    
    const testPath = 'farms/KWct4gdzPvEJfS4XP35C/animals';
    console.log(`Testing path: ${testPath}`);
    
    const animals = await firestoreService.getDocuments(testPath);
    console.log(`✅ Got ${animals.length} animals`);
    
    if (animals.length > 0) {
      console.log('   Sample animal:', animals[0]);
    }
    
    // Test lookupService
    console.log('\n2️⃣ Testing lookupService...');
    const lookupService = require('./functions/mcp/services/lookupService');
    
    const lookups = await lookupService.getLookupsByCategory('Animal Type');
    console.log(`✅ Got ${lookups.length} animal type lookups`);
    
    // Test models
    console.log('\n3️⃣ Testing models...');
    const EntityModel = require('./functions/mcp/models/entityModel');
    const TaskModel = require('./functions/mcp/models/taskModel');
    
    const animalPath = EntityModel.getCollectionPath('KWct4gdzPvEJfS4XP35C', 'animals');
    console.log(`✅ Animal collection path: ${animalPath}`);
    
    const taskPath = TaskModel.getCollectionPath('KWct4gdzPvEJfS4XP35C');
    console.log(`✅ Task collection path: ${taskPath}`);
    
  } catch (error) {
    console.log('❌ MCP integration test failed:', error.message);
    console.log('   Stack:', error.stack);
  }
}

// Run all configuration tests
async function runConfigTests() {
  console.log('🚀 Starting Firebase Configuration Tests...\n');
  
  await testFirebaseConfig();
  await testMCPIntegration();
  
  console.log('\n🏁 Configuration tests completed!');
}

runConfigTests().catch(console.error);
