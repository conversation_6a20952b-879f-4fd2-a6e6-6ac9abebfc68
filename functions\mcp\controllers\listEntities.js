const firestoreService = require('../services/firestoreService');
const lookupService = require('../services/lookupService');
const EntityModel = require('../models/entityModel');
const buildResponse = require('../utils/buildResponse');

/**
 * Controller for listing farm entities
 * Handles listing of animals, plants, zones, machinery, and inventory
 */
class ListEntitiesController {
  /**
   * Handle entity listing request
   * @param {object} intentData - Parsed intent data from LLM
   * @param {object} context - User and farm context
   * @returns {object} Response with entity list
   */
  async handle(intentData, context) {
    try {
      const { entity, filters = {} } = intentData;
      const { farmId } = context;

      // Validate entity type
      const validEntities = ['animals', 'plants', 'zones', 'machinery', 'inventory'];
      if (!validEntities.includes(entity)) {
        return buildResponse.error(`Invalid entity type: ${entity}`);
      }

      // Get collection path
      const collectionPath = EntityModel.getCollectionPath(farmId, entity);
      if (!collectionPath) {
        return buildResponse.error(`Collection path not found for entity: ${entity}`);
      }

      console.log(`🔍 Debug - Collection path: ${collectionPath}`);
      console.log(`🔍 Debug - Farm ID: ${farmId}`);
      console.log(`🔍 Debug - Entity type: ${entity}`);

      // Create Firestore filters
      const firestoreFilters = EntityModel.createFilters(filters);
      console.log(`🔍 Debug - Filters:`, firestoreFilters);

      // Get entities from Firestore
      let entities = await firestoreService.getDocuments(collectionPath, firestoreFilters);
      console.log(`🔍 Debug - Found ${entities.length} entities`);

      // Apply additional filters that can't be done in Firestore
      entities = await this.applyAdditionalFilters(entities, filters, entity, farmId);

      // Resolve lookups for better display
      entities = await this.resolveLookups(entities, entity);

      // Format entities for response
      const formattedEntities = entities.map(entity => 
        buildResponse.formatEntity(entity)
      );

      return buildResponse.entityList(formattedEntities, entity, filters);

    } catch (error) {
      console.error('ListEntities Error:', error);
      return buildResponse.error('Failed to list entities');
    }
  }

  /**
   * Apply additional filters that can't be done in Firestore queries
   * @param {Array} entities - Entities from Firestore
   * @param {object} filters - Filter criteria
   * @param {string} entityType - Type of entity
   * @param {string} farmId - Farm ID
   * @returns {Array} Filtered entities
   */
  async applyAdditionalFilters(entities, filters, entityType, farmId) {
    let filtered = [...entities];

    // Date range filter
    if (filters.dateRange) {
      const startDate = new Date(filters.dateRange.start);
      const endDate = new Date(filters.dateRange.end);
      
      filtered = filtered.filter(entity => {
        const createdAt = new Date(entity.createdAt);
        return createdAt >= startDate && createdAt <= endDate;
      });
    }

    // Recent health check filter (for animals)
    if (filters.recentHealthCheck && entityType === 'animals') {
      const recentEntities = [];
      
      for (const animal of filtered) {
        const healthChecks = await firestoreService.getSubcollectionDocuments(
          `farms/${farmId}/animals/${animal.id}`,
          'healthChecks',
          {}
        );
        
        // Check if there's a health check in the last 30 days
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        
        const recentHealthCheck = healthChecks.some(check => 
          new Date(check.createdAt) > thirtyDaysAgo
        );
        
        if (recentHealthCheck) {
          recentEntities.push({
            ...animal,
            recentHealthCheck: true,
            lastHealthCheckDate: Math.max(...healthChecks.map(check => 
              new Date(check.createdAt).getTime()
            ))
          });
        }
      }
      
      filtered = recentEntities;
    }

    // Low stock filter (for inventory)
    if (filters.lowStock && entityType === 'inventory') {
      filtered = filtered.filter(item => 
        item.quantity <= (item.minQuantity || 0)
      );
    }

    // Expired items filter (for inventory)
    if (filters.expired && entityType === 'inventory') {
      const now = new Date();
      filtered = filtered.filter(item => 
        item.expiryDate && new Date(item.expiryDate) < now
      );
    }

    return filtered;
  }

  /**
   * Resolve lookup IDs to titles for better display
   * @param {Array} entities - Entities to resolve
   * @param {string} entityType - Type of entity
   * @returns {Array} Entities with resolved lookups
   */
  async resolveLookups(entities, entityType) {
    const lookupFields = this.getLookupFields(entityType);
    
    const resolvedEntities = [];
    
    for (const entity of entities) {
      const resolved = await lookupService.resolveLookups(entity, lookupFields);
      resolvedEntities.push(resolved);
    }
    
    return resolvedEntities;
  }

  /**
   * Get lookup fields for entity type
   * @param {string} entityType - Type of entity
   * @returns {Array} Array of lookup field names
   */
  getLookupFields(entityType) {
    const lookupFields = {
      'animals': ['typeId', 'breedId', 'zoneId'],
      'plants': ['varietyId', 'cropTypeId', 'zoneId'],
      'zones': [],
      'machinery': ['typeId'],
      'inventory': ['categoryId', 'typeId']
    };

    return lookupFields[entityType] || [];
  }

  /**
   * Get entities by specific criteria (helper method)
   * @param {string} farmId - Farm ID
   * @param {string} entityType - Entity type
   * @param {string} field - Field to search
   * @param {any} value - Value to search for
   * @returns {Array} Matching entities
   */
  async getEntitiesByField(farmId, entityType, field, value) {
    try {
      const collectionPath = EntityModel.getCollectionPath(farmId, entityType);
      const filters = { [field]: value };
      
      const entities = await firestoreService.getDocuments(collectionPath, filters);
      return await this.resolveLookups(entities, entityType);
      
    } catch (error) {
      console.error('Error getting entities by field:', error);
      return [];
    }
  }

  /**
   * Get entity statistics
   * @param {string} farmId - Farm ID
   * @param {string} entityType - Entity type
   * @returns {object} Entity statistics
   */
  async getEntityStats(farmId, entityType) {
    try {
      const collectionPath = EntityModel.getCollectionPath(farmId, entityType);
      const entities = await firestoreService.getDocuments(collectionPath);
      
      const stats = {
        total: entities.length,
        active: entities.filter(e => e.status === 'active').length,
        inactive: entities.filter(e => e.status !== 'active').length
      };

      // Entity-specific stats
      if (entityType === 'animals') {
        stats.healthy = entities.filter(e => e.healthStatus === 'healthy').length;
        stats.sick = entities.filter(e => e.healthStatus === 'sick').length;
        stats.male = entities.filter(e => e.gender === 'male').length;
        stats.female = entities.filter(e => e.gender === 'female').length;
      }

      if (entityType === 'inventory') {
        stats.lowStock = entities.filter(e => e.quantity <= (e.minQuantity || 0)).length;
        stats.outOfStock = entities.filter(e => e.quantity === 0).length;
      }

      return stats;
      
    } catch (error) {
      console.error('Error getting entity stats:', error);
      return {};
    }
  }
}

module.exports = new ListEntitiesController();
