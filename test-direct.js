// Direct test of MCP functionality
const admin = require('firebase-admin');

// Initialize Firebase Admin for local testing
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: 'kissandost-9570f'
  });
}

// Set environment variables
process.env.OPENROUTER_API_KEY = '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';
process.env.OPENROUTER_MODEL = 'anthropic/claude-3.5-sonnet';

// Import MCP components
const router = require('./functions/mcp/router');

async function testMCP() {
  console.log('🧪 Testing MCP Server Components...\n');

  const testCases = [
    {
      name: 'List Animals Intent',
      request: {
        text: 'Show all animals',
        userId: 'rir9S0uQlhRVp9cmhLJZXT54Atc2', // Use a real user ID from your test data
        farmId: 'KWct4gdzPvEJfS4XP35C' // Use the farmId from your add-test-data.js script
      }
    },
    {
      name: 'Assign Task Intent',
      request: {
        text: 'Assign watering task to Adnan for tomorrow', // Use a user from your test data
        userId: 'rir9S0uQlhRVp9cmhLJZXT54Atc2', // Use the ID of an admin/owner user
        farmId: 'KWct4gdzPvEJfS4XP35C'
      }
    },
    {
      name: 'Create Health Check Intent',
      request: {
        text: 'Create health check for animal BUFF-91',
        userId: 'rir9S0uQlhRVp9cmhLJZXT54Atc2',
        farmId: 'KWct4gdzPvEJfS4XP35C'
      }
    }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`📋 Testing: ${testCase.name}`);
      console.log(`📤 Request: "${testCase.request.text}"`);
      
      const result = await router.processRequest(testCase.request);
      
      console.log(`✅ Status: ${result.status}`);
      console.log(`💬 Message: ${result.message}`);
      
      if (result.data) {
        console.log(`📊 Data:`, JSON.stringify(result.data, null, 2));
      }
      
      if (result.meta) {
        console.log(`📋 Meta:`, JSON.stringify(result.meta, null, 2));
      }
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      console.log(`📄 Stack:`, error.stack);
    }
    
    console.log('---\n');
  }
}

// Test individual components first
async function testComponents() {
  console.log('🔧 Testing Individual Components...\n');

  try {
    // Test OpenRouter
    console.log('📡 Testing OpenRouter...');
    const OpenRouter = require('./functions/mcp/openRouter');
    
    const intentData = await OpenRouter.parseIntent('Show all animals', {
      userId: 'test_user_123',
      farmId: 'test_farm_456'
    });
    
    console.log('✅ OpenRouter working!');
    console.log('📊 Intent Data:', JSON.stringify(intentData, null, 2));
    
  } catch (error) {
    console.log('❌ OpenRouter Error:', error.message);
  }

  console.log('---\n');

  try {
    // Test Firestore Service
    console.log('💾 Testing Firestore Service...');
    const firestoreService = require('./functions/mcp/services/firestoreService');
    
    // Try to get documents (will be empty but should not error)
    const animals = await firestoreService.getDocuments('farms/test_farm_456/animals');
    console.log('✅ Firestore Service working!');
    console.log(`📊 Found ${animals.length} animals`);
    
  } catch (error) {
    console.log('❌ Firestore Service Error:', error.message);
  }

  console.log('---\n');
}

// Run tests
async function runAllTests() {
  await testComponents();
  await testMCP();
  console.log('🏁 All tests completed!');
}

runAllTests().catch(console.error);
