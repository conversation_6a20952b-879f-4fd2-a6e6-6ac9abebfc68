// Debug script to check your actual Firestore data
const admin = require('firebase-admin');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: 'kissandost-9570f' // Your actual Firebase project ID
  });
}

const db = admin.firestore();

async function debugFirestoreData() {
  console.log('🔍 Debugging Firestore Data...\n');
  
  const farmId = 'KWct4gdzPvEJfS4XP35C'; // Your actual farm ID
  const userId = 'rir9S0uQlhRVp9cmhLJZXT54Atc2'; // Your actual user ID
  
  try {
    // 1. Check if farm exists
    console.log('📋 Checking farm...');
    const farm = await db.collection('farms').doc(farmId).get();
    if (farm.exists) {
      console.log('✅ Farm exists:', farm.data());
    } else {
      console.log('❌ Farm does not exist');
    }
    console.log('');

    // 2. Check animals collection
    console.log('🐄 Checking animals...');
    const animalsPath = `farms/${farmId}/animals`;
    console.log(`Collection path: ${animalsPath}`);
    
    const animalsSnapshot = await db.collection(animalsPath).get();
    console.log(`Found ${animalsSnapshot.size} animals`);
    
    animalsSnapshot.forEach(doc => {
      console.log(`- Animal ID: ${doc.id}`, doc.data());
    });
    console.log('');

    // 3. Check plants collection
    console.log('🌱 Checking plants...');
    const plantsPath = `farms/${farmId}/plants`;
    console.log(`Collection path: ${plantsPath}`);
    
    const plantsSnapshot = await db.collection(plantsPath).get();
    console.log(`Found ${plantsSnapshot.size} plants`);
    
    plantsSnapshot.forEach(doc => {
      console.log(`- Plant ID: ${doc.id}`, doc.data());
    });
    console.log('');

    // 4. Check zones collection
    console.log('🏞️ Checking zones...');
    const zonesPath = `farms/${farmId}/zones`;
    console.log(`Collection path: ${zonesPath}`);
    
    const zonesSnapshot = await db.collection(zonesPath).get();
    console.log(`Found ${zonesSnapshot.size} zones`);
    
    zonesSnapshot.forEach(doc => {
      console.log(`- Zone ID: ${doc.id}`, doc.data());
    });
    console.log('');

    // 5. Check users collection
    console.log('👥 Checking users...');
    const usersPath = `farms/${farmId}/users`;
    console.log(`Collection path: ${usersPath}`);
    
    const usersSnapshot = await db.collection(usersPath).get();
    console.log(`Found ${usersSnapshot.size} users`);
    
    usersSnapshot.forEach(doc => {
      console.log(`- User ID: ${doc.id}`, doc.data());
    });
    console.log('');

    // 6. Check if specific user exists
    console.log('🔍 Checking specific user...');
    const user = await db.collection(usersPath).doc(userId).get();
    if (user.exists) {
      console.log('✅ User exists:', user.data());
    } else {
      console.log('❌ User does not exist');
      
      // Check if user exists by name "Adnan"
      const usersQuery = await db.collection(usersPath).where('name', '==', 'Adnan').get();
      if (!usersQuery.empty) {
        console.log('✅ Found user "Adnan":');
        usersQuery.forEach(doc => {
          console.log(`- User ID: ${doc.id}`, doc.data());
        });
      } else {
        console.log('❌ No user named "Adnan" found');
      }
    }
    console.log('');

    // 7. Check if Zone 3 exists
    console.log('🔍 Checking Zone 3...');
    const zone3Query = await db.collection(zonesPath).where('name', '==', 'Zone 3').get();
    if (!zone3Query.empty) {
      console.log('✅ Found Zone 3:');
      zone3Query.forEach(doc => {
        console.log(`- Zone ID: ${doc.id}`, doc.data());
      });
    } else {
      console.log('❌ Zone 3 not found');
      
      // List all zone names
      const allZones = await db.collection(zonesPath).get();
      console.log('Available zones:');
      allZones.forEach(doc => {
        const data = doc.data();
        console.log(`- ${data.name || 'Unnamed'} (ID: ${doc.id})`);
      });
    }
    console.log('');

    // 8. Check tasks collection
    console.log('📋 Checking tasks...');
    const tasksPath = `farms/${farmId}/tasks`;
    console.log(`Collection path: ${tasksPath}`);
    
    const tasksSnapshot = await db.collection(tasksPath).get();
    console.log(`Found ${tasksSnapshot.size} tasks`);
    
    tasksSnapshot.forEach(doc => {
      console.log(`- Task ID: ${doc.id}`, doc.data());
    });
    console.log('');

    // 9. Check lookups
    console.log('📚 Checking lookups...');
    const lookupsSnapshot = await db.collection('lookups').get();
    console.log(`Found ${lookupsSnapshot.size} lookups`);
    
    lookupsSnapshot.forEach(doc => {
      console.log(`- Lookup ID: ${doc.id}`, doc.data());
    });

  } catch (error) {
    console.error('❌ Error debugging Firestore:', error);
  }
}

// Also test the MCP components with your data
async function testMCPWithYourData() {
  console.log('\n🧪 Testing MCP with your data...\n');
  
  // Set environment variables
  process.env.OPENROUTER_API_KEY = '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';
  
  try {
    const router = require('./functions/mcp/router');
    
    const testRequest = {
      text: "Show all animals",
      userId: "rir9S0uQlhRVp9cmhLJZXT54Atc2",
      farmId: "KWct4gdzPvEJfS4XP35C"
    };
    
    console.log('📤 Testing request:', testRequest);
    
    const result = await router.processRequest(testRequest);
    
    console.log('📥 Result:', JSON.stringify(result, null, 2));
    
  } catch (error) {
    console.error('❌ Error testing MCP:', error);
  }
}

async function runDebug() {
  await debugFirestoreData();
  await testMCPWithYourData();
  console.log('\n🏁 Debug completed!');
}

runDebug().catch(console.error);
