const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:5001/demo-project/us-central1/mcp';
const TEST_DATA = {
  userId: 'test_user_123',
  farmId: 'test_farm_456'
};

// Test cases
const testCases = [
  {
    name: 'List Animals',
    request: {
      text: 'Show all animals',
      ...TEST_DATA
    }
  },
  {
    name: 'Assign Task',
    request: {
      text: 'Assign watering task to <PERSON> in Zone 1 for tomorrow',
      ...TEST_DATA
    }
  },
  {
    name: 'Complete Task',
    request: {
      text: 'Mark feeding task as complete',
      ...TEST_DATA
    }
  },
  {
    name: 'Create Health Check',
    request: {
      text: 'Create health check for animal BUFF-91 with good condition',
      ...TEST_DATA
    }
  },
  {
    name: 'List Plants with Filter',
    request: {
      text: 'Show tomato plants in Zone 2',
      ...TEST_DATA
    }
  }
];

async function runTests() {
  console.log('🧪 Starting MCP Server Tests...\n');

  for (const testCase of testCases) {
    try {
      console.log(`📋 Testing: ${testCase.name}`);
      console.log(`📤 Request: "${testCase.request.text}"`);
      
      const response = await axios.post(BASE_URL, testCase.request, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      console.log(`✅ Status: ${response.data.status}`);
      console.log(`💬 Message: ${response.data.message}`);
      
      if (response.data.data) {
        console.log(`📊 Data:`, JSON.stringify(response.data.data, null, 2));
      }
      
      console.log('---\n');

    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      
      if (error.response) {
        console.log(`📄 Response:`, error.response.data);
      }
      
      console.log('---\n');
    }
  }

  console.log('🏁 Tests completed!');
}

// Health check function
async function healthCheck() {
  try {
    console.log('🔍 Checking MCP Server health...');
    
    const response = await axios.post(BASE_URL, {
      text: 'health check',
      ...TEST_DATA
    });

    console.log('✅ Server is running!');
    console.log('Response:', response.data);
    
  } catch (error) {
    console.log('❌ Server is not responding');
    console.log('Error:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure to start the Firebase emulators first:');
      console.log('   firebase emulators:start');
    }
  }
}

// Command line interface
const command = process.argv[2];

switch (command) {
  case 'health':
    healthCheck();
    break;
  case 'test':
  default:
    runTests();
    break;
}

// Export for programmatic use
module.exports = {
  runTests,
  healthCheck,
  testCases
};
