// Set up environment variables for Firebase emulators
process.env.FIRESTORE_EMULATOR_HOST = 'localhost:8080';
process.env.FIREBASE_AUTH_EMULATOR_HOST = 'localhost:9099';

const admin = require('firebase-admin');

// Initialize Firebase Admin for emulator
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: 'kissandost-9570f'
  });
}

const db = admin.firestore();

// Your actual data
const FARM_ID = 'KWct4gdzPvEJfS4XP35C';
const USER_ID = 'rir9S0uQlhRVp9cmhLJZXT54Atc2';

async function seedEmulatorData() {
  console.log('🌱 Seeding Firebase Emulator with test data...\n');
  
  try {
    // Add farm
    console.log('📋 Creating farm...');
    await db.collection('farms').doc(FARM_ID).set({
      name: 'Test Farm',
      owner: USER_ID,
      location: 'Test Location',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Add users
    console.log('👥 Creating users...');
    await db.collection('farms').doc(FARM_ID).collection('users').doc(USER_ID).set({
      name: 'Test Admin',
      role: 'admin',
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    await db.collection('farms').doc(FARM_ID).collection('users').doc('user_adnan').set({
      name: 'Adnan',
      role: 'worker',
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Add zones
    console.log('🏞️ Creating zones...');
    await db.collection('farms').doc(FARM_ID).collection('zones').doc('zone_3').set({
      name: 'Zone 3',
      type: 'field',
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Add animals
    console.log('🐄 Creating animals...');
    await db.collection('farms').doc(FARM_ID).collection('animals').doc('animal_1').set({
      tagId: 'BUFF-001',
      name: 'Bella',
      typeId: 'buffalo',
      status: 'active',
      healthStatus: 'healthy',
      zoneId: 'zone_3',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    await db.collection('farms').doc(FARM_ID).collection('animals').doc('animal_2').set({
      tagId: 'COW-001',
      name: 'Moti',
      typeId: 'cow',
      status: 'active',
      healthStatus: 'healthy',
      zoneId: 'zone_3',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Add plants
    console.log('🌱 Creating plants...');
    await db.collection('farms').doc(FARM_ID).collection('plants').doc('plant_1').set({
      name: 'Tomato Crop',
      varietyId: 'tomato',
      status: 'growing',
      healthStatus: 'healthy',
      zoneId: 'zone_3',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    await db.collection('farms').doc(FARM_ID).collection('plants').doc('plant_2').set({
      name: 'Wheat Field',
      varietyId: 'wheat',
      status: 'growing',
      healthStatus: 'healthy',
      zoneId: 'zone_3',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Add lookups
    console.log('📚 Creating lookups...');
    const lookups = [
      { id: 'buffalo', title: 'Buffalo', categoryName: 'Animal Type', type: 'animal' },
      { id: 'cow', title: 'Cow', categoryName: 'Animal Type', type: 'animal' },
      { id: 'tomato', title: 'Tomato', categoryName: 'Plant Variety', type: 'plant' },
      { id: 'wheat', title: 'Wheat', categoryName: 'Plant Variety', type: 'plant' }
    ];

    for (const lookup of lookups) {
      await db.collection('lookups').doc(lookup.id).set({
        title: lookup.title,
        categoryName: lookup.categoryName,
        type: lookup.type,
        parentId: null,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
    }

    console.log('✅ Emulator data seeded successfully!');
    
    // Verify data
    const animalsSnapshot = await db.collection(`farms/${FARM_ID}/animals`).get();
    const plantsSnapshot = await db.collection(`farms/${FARM_ID}/plants`).get();
    const usersSnapshot = await db.collection(`farms/${FARM_ID}/users`).get();
    const zonesSnapshot = await db.collection(`farms/${FARM_ID}/zones`).get();
    
    console.log('\n📊 Summary:');
    console.log(`- Animals: ${animalsSnapshot.size}`);
    console.log(`- Plants: ${plantsSnapshot.size}`);
    console.log(`- Users: ${usersSnapshot.size}`);
    console.log(`- Zones: ${zonesSnapshot.size}`);
    
    console.log('\n🎉 Ready for MCP testing!');
    
  } catch (error) {
    console.error('❌ Error seeding emulator data:', error);
  }
}

seedEmulatorData().then(() => {
  process.exit(0);
}).catch(console.error);
