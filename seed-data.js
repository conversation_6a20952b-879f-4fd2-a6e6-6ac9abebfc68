const admin = require('firebase-admin');

// Initialize Firebase Admin (for local testing)
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: 'demo-project' // Using demo project for local testing
  });
}

const db = admin.firestore();

// Sample data
const sampleData = {
  farms: {
    'test_farm_456': {
      name: 'Green Valley Farm',
      owner: 'test_user_123',
      location: 'Punjab, Pakistan',
      area: 50,
      areaUnit: 'acres',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  },
  
  users: {
    'test_user_123': {
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'owner',
      status: 'active'
    },
    'user_john_456': {
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'caretaker',
      status: 'active'
    },
    'user_sarah_789': {
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'worker',
      status: 'active'
    }
  },

  zones: {
    'zone_1': {
      name: 'Zone 1',
      type: 'field',
      area: 10,
      areaUnit: 'acres',
      status: 'active'
    },
    'zone_2': {
      name: 'Zone 2',
      type: 'garden',
      area: 5,
      areaUnit: 'acres',
      status: 'active'
    },
    'zone_barn': {
      name: 'Main Barn',
      type: 'barn',
      capacity: 50,
      currentOccupancy: 25,
      status: 'active'
    }
  },

  animals: {
    'animal_buff_91': {
      tagId: 'BUFF-91',
      name: 'Bella',
      typeId: 'lookup_buffalo',
      gender: 'female',
      birthDate: '2020-03-15',
      weight: 450,
      zoneId: 'zone_barn',
      status: 'active',
      healthStatus: 'healthy'
    },
    'animal_cow_23': {
      tagId: 'COW-23',
      name: 'Moti',
      typeId: 'lookup_cow',
      gender: 'male',
      birthDate: '2019-08-10',
      weight: 380,
      zoneId: 'zone_barn',
      status: 'active',
      healthStatus: 'healthy'
    }
  },

  plants: {
    'plant_tomato_1': {
      name: 'Tomato Crop 1',
      varietyId: 'lookup_tomato_variety',
      zoneId: 'zone_1',
      plantedDate: '2025-06-01',
      expectedHarvestDate: '2025-09-01',
      quantity: 1000,
      unit: 'plants',
      status: 'growing',
      healthStatus: 'healthy'
    },
    'plant_wheat_2': {
      name: 'Wheat Field 2',
      varietyId: 'lookup_wheat_variety',
      zoneId: 'zone_2',
      plantedDate: '2025-05-15',
      expectedHarvestDate: '2025-10-15',
      quantity: 5,
      unit: 'acres',
      status: 'growing',
      healthStatus: 'healthy'
    }
  },

  tasks: {
    'task_feeding_1': {
      title: 'Morning Feeding',
      description: 'Feed all animals in the barn',
      assignedTo: 'user_john_456',
      assignedBy: 'test_user_123',
      zoneId: 'zone_barn',
      priority: 'high',
      status: 'pending',
      dueDate: '2025-07-31'
    },
    'task_watering_1': {
      title: 'Water Tomato Plants',
      description: 'Water the tomato plants in Zone 1',
      assignedTo: 'user_sarah_789',
      assignedBy: 'test_user_123',
      zoneId: 'zone_1',
      entityId: 'plant_tomato_1',
      entityType: 'plants',
      priority: 'medium',
      status: 'pending',
      dueDate: '2025-07-30'
    }
  },

  lookups: {
    'lookup_buffalo': {
      title: 'Buffalo',
      categoryName: 'Animal Type',
      type: 'animal',
      parentId: null
    },
    'lookup_cow': {
      title: 'Cow',
      categoryName: 'Animal Type',
      type: 'animal',
      parentId: null
    },
    'lookup_tomato_variety': {
      title: 'Roma Tomato',
      categoryName: 'Plant Variety',
      type: 'plant',
      parentId: null
    },
    'lookup_wheat_variety': {
      title: 'Punjab Wheat',
      categoryName: 'Plant Variety',
      type: 'plant',
      parentId: null
    }
  }
};

async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');

    const farmId = 'test_farm_456';

    // Create farm document
    await db.collection('farms').doc(farmId).set(sampleData.farms[farmId]);
    console.log('✅ Farm created');

    // Create farm users
    for (const [userId, userData] of Object.entries(sampleData.users)) {
      await db.collection('farms').doc(farmId).collection('users').doc(userId).set({
        ...userData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
    console.log('✅ Users created');

    // Create zones
    for (const [zoneId, zoneData] of Object.entries(sampleData.zones)) {
      await db.collection('farms').doc(farmId).collection('zones').doc(zoneId).set({
        ...zoneData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
    console.log('✅ Zones created');

    // Create animals
    for (const [animalId, animalData] of Object.entries(sampleData.animals)) {
      await db.collection('farms').doc(farmId).collection('animals').doc(animalId).set({
        ...animalData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
    console.log('✅ Animals created');

    // Create plants
    for (const [plantId, plantData] of Object.entries(sampleData.plants)) {
      await db.collection('farms').doc(farmId).collection('plants').doc(plantId).set({
        ...plantData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
    console.log('✅ Plants created');

    // Create tasks
    for (const [taskId, taskData] of Object.entries(sampleData.tasks)) {
      await db.collection('farms').doc(farmId).collection('tasks').doc(taskId).set({
        ...taskData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
    console.log('✅ Tasks created');

    // Create lookups
    for (const [lookupId, lookupData] of Object.entries(sampleData.lookups)) {
      await db.collection('lookups').doc(lookupId).set({
        ...lookupData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
    console.log('✅ Lookups created');

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`- Farm: ${farmId}`);
    console.log(`- Users: ${Object.keys(sampleData.users).length}`);
    console.log(`- Zones: ${Object.keys(sampleData.zones).length}`);
    console.log(`- Animals: ${Object.keys(sampleData.animals).length}`);
    console.log(`- Plants: ${Object.keys(sampleData.plants).length}`);
    console.log(`- Tasks: ${Object.keys(sampleData.tasks).length}`);
    console.log(`- Lookups: ${Object.keys(sampleData.lookups).length}`);

  } catch (error) {
    console.error('❌ Error seeding database:', error);
  }
}

async function clearDatabase() {
  try {
    console.log('🧹 Clearing test data...');

    const farmId = 'test_farm_456';
    
    // Delete farm and all subcollections
    await db.collection('farms').doc(farmId).delete();
    
    // Delete lookups
    for (const lookupId of Object.keys(sampleData.lookups)) {
      await db.collection('lookups').doc(lookupId).delete();
    }

    console.log('✅ Test data cleared');

  } catch (error) {
    console.error('❌ Error clearing database:', error);
  }
}

// Command line interface
const command = process.argv[2];

switch (command) {
  case 'seed':
    seedDatabase();
    break;
  case 'clear':
    clearDatabase();
    break;
  default:
    console.log('Usage: node seed-data.js [seed|clear]');
    break;
}

module.exports = {
  seedDatabase,
  clearDatabase,
  sampleData
};
