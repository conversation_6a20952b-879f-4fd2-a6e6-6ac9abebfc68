const axios = require('axios');
const BuildPrompt = require('./utils/buildPrompt');

/**
 * OpenRouter API client for LLM communication
 * Handles intent parsing and natural language processing
 */
class OpenRouter {
  constructor() {
    this.apiKey = process.env.OPENROUTER_API_KEY;
    this.baseUrl = 'https://openrouter.ai/api/v1';
    this.model = 'anthropic/claude-3.5-sonnet'; // Default model
  }

  /**
   * Parse user intent from natural language text
   * @param {string} text - User input text
   * @param {object} context - User and farm context
   * @returns {object} Parsed intent data
   */
  async parseIntent(text, context) {
    try {
      const prompt = BuildPrompt.createIntentPrompt(text, context);
      
      const response = await axios.post(
        `${this.baseUrl}/chat/completions`,
        {
          model: this.model,
          messages: [
            {
              role: 'system',
              content: prompt.systemPrompt
            },
            {
              role: 'user',
              content: prompt.userPrompt
            }
          ],
          temperature: 0.1,
          max_tokens: 500
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://kissan-dost.com',
            'X-Title': 'Kissan Dost MCP Server'
          }
        }
      );

      const content = response.data.choices[0].message.content;
      
      // Parse JSON response from LLM
      try {
        const intentData = JSON.parse(content);
        return intentData;
      } catch (parseError) {
        console.error('Failed to parse LLM response as JSON:', content);
        return null;
      }

    } catch (error) {
      console.error('OpenRouter API Error:', error.response?.data || error.message);
      throw new Error('Failed to parse intent with LLM');
    }
  }

  /**
   * Generate confirmation message for actions
   * @param {object} actionData - Action details
   * @returns {string} Confirmation message
   */
  async generateConfirmation(actionData) {
    try {
      const prompt = BuildPrompt.createConfirmationPrompt(actionData);
      
      const response = await axios.post(
        `${this.baseUrl}/chat/completions`,
        {
          model: this.model,
          messages: [
            {
              role: 'system',
              content: prompt.systemPrompt
            },
            {
              role: 'user',
              content: prompt.userPrompt
            }
          ],
          temperature: 0.3,
          max_tokens: 200
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://kissan-dost.com',
            'X-Title': 'Kissan Dost MCP Server'
          }
        }
      );

      return response.data.choices[0].message.content.trim();

    } catch (error) {
      console.error('OpenRouter Confirmation Error:', error.response?.data || error.message);
      return 'Action completed successfully.';
    }
  }
}

module.exports = new OpenRouter();
