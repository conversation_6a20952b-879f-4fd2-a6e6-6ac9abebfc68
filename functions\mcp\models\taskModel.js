/**
 * Task model for farm task management
 * Handles task creation, validation, and data structure
 */
class TaskModel {
  /**
   * Create a new task object
   * @param {object} data - Task data
   * @returns {object} Formatted task object
   */
  static create(data) {
    const now = new Date();
    
    return {
      title: data.title || '',
      description: data.description || '',
      assignedTo: data.assignedTo || null,
      assignedBy: data.assignedBy || null,
      zoneId: data.zoneId || null,
      entityId: data.entityId || null, // Animal, plant, or machinery ID
      entityType: data.entityType || null, // 'animal', 'plant', 'machinery'
      priority: data.priority || 'medium', // 'low', 'medium', 'high'
      status: data.status || 'pending', // 'pending', 'in_progress', 'completed', 'cancelled'
      dueDate: data.dueDate || null,
      completedAt: null,
      completedBy: null,
      notes: data.notes || '',
      tags: data.tags || [],
      recurring: data.recurring || false,
      recurringPattern: data.recurringPattern || null, // 'daily', 'weekly', 'monthly'
      createdAt: now,
      updatedAt: now
    };
  }

  /**
   * Update task status
   * @param {object} task - Existing task
   * @param {string} status - New status
   * @param {string} userId - User making the change
   * @returns {object} Updated task
   */
  static updateStatus(task, status, userId) {
    const updates = {
      status: status,
      updatedAt: new Date()
    };

    if (status === 'completed') {
      updates.completedAt = new Date();
      updates.completedBy = userId;
    }

    return {
      ...task,
      ...updates
    };
  }

  /**
   * Validate task data
   * @param {object} data - Task data to validate
   * @returns {object} Validation result
   */
  static validate(data) {
    const errors = [];

    if (!data.title || data.title.trim().length === 0) {
      errors.push('Title is required');
    }

    if (!data.assignedTo) {
      errors.push('Assigned user is required');
    }

    if (data.priority && !['low', 'medium', 'high'].includes(data.priority)) {
      errors.push('Priority must be low, medium, or high');
    }

    if (data.status && !['pending', 'in_progress', 'completed', 'cancelled'].includes(data.status)) {
      errors.push('Invalid status value');
    }

    if (data.dueDate) {
      const dueDate = new Date(data.dueDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Set to start of today

      console.log(`🔍 Debug - Due date validation:`);
      console.log(`  - Input dueDate: ${data.dueDate}`);
      console.log(`  - Parsed dueDate: ${dueDate}`);
      console.log(`  - Today (start): ${today}`);
      console.log(`  - Is past?: ${dueDate < today}`);

      if (isNaN(dueDate.getTime())) {
        errors.push('Invalid due date format');
      } else if (dueDate < today) {
        errors.push('Due date cannot be in the past');
      }
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * Format task for display
   * @param {object} task - Task object
   * @param {object} lookups - Resolved lookup data
   * @returns {object} Formatted task
   */
  static format(task, lookups = {}) {
    return {
      id: task.id,
      title: task.title,
      description: task.description,
      assignedTo: task.assignedTo,
      assignedToName: lookups.assignedToName || task.assignedTo,
      zone: lookups.zoneName || task.zoneId,
      entity: lookups.entityName || task.entityId,
      entityType: task.entityType,
      priority: task.priority,
      status: task.status,
      dueDate: task.dueDate,
      completedAt: task.completedAt,
      notes: task.notes,
      tags: task.tags,
      createdAt: task.createdAt
    };
  }

  /**
   * Get task collection path for a farm
   * @param {string} farmId - Farm ID
   * @returns {string} Collection path
   */
  static getCollectionPath(farmId) {
    return `farms/${farmId}/tasks`;
  }

  /**
   * Create task filters from search criteria
   * @param {object} criteria - Search criteria
   * @returns {object} Firestore filters
   */
  static createFilters(criteria) {
    const filters = {};

    if (criteria.assignedTo) {
      filters.assignedTo = criteria.assignedTo;
    }

    if (criteria.status) {
      filters.status = criteria.status;
    }

    if (criteria.priority) {
      filters.priority = criteria.priority;
    }

    if (criteria.zoneId) {
      filters.zoneId = criteria.zoneId;
    }

    if (criteria.entityType) {
      filters.entityType = criteria.entityType;
    }

    return filters;
  }
}

module.exports = TaskModel;
