const admin = require('firebase-admin');

/**
 * Firestore service for database operations
 * Provides CRUD operations for all collections
 */
class FirestoreService {
  constructor() {
    this.db = null;
  }

  getDb() {
    if (!this.db) {
      this.db = admin.firestore();
    }
    return this.db;
  }

  /**
   * Get documents from a collection with optional filters
   * @param {string} collectionPath - Path to collection
   * @param {object} filters - Filter conditions
   * @returns {Array} Array of documents
   */
  async getDocuments(collectionPath, filters = {}) {
    try {
      let query = this.getDb().collection(collectionPath);

      // Apply filters
      Object.entries(filters).forEach(([field, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            query = query.where(field, 'in', value);
          } else {
            query = query.where(field, '==', value);
          }
        }
      });

      const snapshot = await query.get();
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

    } catch (error) {
      console.error('Error getting documents:', error);
      throw error;
    }
  }

  /**
   * Get a single document by ID
   * @param {string} collectionPath - Path to collection
   * @param {string} docId - Document ID
   * @returns {object|null} Document data or null
   */
  async getDocument(collectionPath, docId) {
    try {
      const doc = await this.getDb().collection(collectionPath).doc(docId).get();
      
      if (!doc.exists) {
        return null;
      }

      return {
        id: doc.id,
        ...doc.data()
      };

    } catch (error) {
      console.error('Error getting document:', error);
      throw error;
    }
  }

  /**
   * Create a new document
   * @param {string} collectionPath - Path to collection
   * @param {object} data - Document data
   * @param {string} docId - Optional document ID
   * @returns {object} Created document with ID
   */
  async createDocument(collectionPath, data, docId = null) {
    try {
      const timestamp = admin.firestore.FieldValue.serverTimestamp();
      const docData = {
        ...data,
        createdAt: timestamp,
        updatedAt: timestamp
      };

      let docRef;
      if (docId) {
        docRef = this.getDb().collection(collectionPath).doc(docId);
        await docRef.set(docData);
      } else {
        docRef = await this.getDb().collection(collectionPath).add(docData);
      }

      return {
        id: docRef.id,
        ...docData
      };

    } catch (error) {
      console.error('Error creating document:', error);
      throw error;
    }
  }

  /**
   * Update an existing document
   * @param {string} collectionPath - Path to collection
   * @param {string} docId - Document ID
   * @param {object} data - Update data
   * @returns {object} Updated document
   */
  async updateDocument(collectionPath, docId, data) {
    try {
      const updateData = {
        ...data,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      };

      await this.getDb().collection(collectionPath).doc(docId).update(updateData);

      return {
        id: docId,
        ...updateData
      };

    } catch (error) {
      console.error('Error updating document:', error);
      throw error;
    }
  }

  /**
   * Delete a document
   * @param {string} collectionPath - Path to collection
   * @param {string} docId - Document ID
   */
  async deleteDocument(collectionPath, docId) {
    try {
      await this.getDb().collection(collectionPath).doc(docId).delete();
    } catch (error) {
      console.error('Error deleting document:', error);
      throw error;
    }
  }

  /**
   * Get documents from subcollection
   * @param {string} parentPath - Parent document path
   * @param {string} subcollection - Subcollection name
   * @param {object} filters - Filter conditions
   * @returns {Array} Array of documents
   */
  async getSubcollectionDocuments(parentPath, subcollection, filters = {}) {
    try {
      const collectionPath = `${parentPath}/${subcollection}`;
      return await this.getDocuments(collectionPath, filters);
    } catch (error) {
      console.error('Error getting subcollection documents:', error);
      throw error;
    }
  }

  /**
   * Create document in subcollection
   * @param {string} parentPath - Parent document path
   * @param {string} subcollection - Subcollection name
   * @param {object} data - Document data
   * @returns {object} Created document
   */
  async createSubcollectionDocument(parentPath, subcollection, data) {
    try {
      const collectionPath = `${parentPath}/${subcollection}`;
      return await this.createDocument(collectionPath, data);
    } catch (error) {
      console.error('Error creating subcollection document:', error);
      throw error;
    }
  }
}

module.exports = new FirestoreService();
