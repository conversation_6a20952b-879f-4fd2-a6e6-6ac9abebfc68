const firestoreService = require('../services/firestoreService');
const lookupService = require('../services/lookupService');
const TaskModel = require('../models/taskModel');
const buildResponse = require('../utils/buildResponse');

/**
 * Controller for assigning tasks
 * Handles task creation and assignment to farm workers
 */
class AssignTaskController {
  /**
   * Handle task assignment request
   * @param {object} intentData - Parsed intent data from LLM
   * @param {object} context - User and farm context
   * @returns {object} Response with task assignment result
   */
  async handle(intentData, context) {
    try {
      const { userId, farmId } = context;
      
      // Extract task data from intent
      const taskData = this.extractTaskData(intentData, userId);

      console.log(`🔍 Debug - Intent data:`, JSON.stringify(intentData, null, 2));
      console.log(`🔍 Debug - Task data:`, JSON.stringify(taskData, null, 2));

      // Validate task data
      const validation = TaskModel.validate(taskData);
      if (!validation.isValid) {
        console.log(`🔍 Debug - Validation errors:`, validation.errors);
        return buildResponse.validationError(validation.errors);
      }

      // Resolve assignee (convert name to user ID if needed)
      const assigneeId = await this.resolveAssignee(taskData.assignedTo, farmId);
      if (!assigneeId) {
        return buildResponse.error(`Could not find user: ${taskData.assignedTo}`);
      }
      taskData.assignedTo = assigneeId;

      // Resolve zone if specified
      if (taskData.zoneId) {
        const zone = await this.resolveZone(taskData.zoneId, farmId);
        if (!zone) {
          return buildResponse.error(`Could not find zone: ${taskData.zoneId}`);
        }
        taskData.zoneId = zone.id;
      }

      // Resolve entity if specified
      if (taskData.entityId && taskData.entityType) {
        const entity = await this.resolveEntity(taskData.entityId, taskData.entityType, farmId);
        if (!entity) {
          return buildResponse.error(`Could not find ${taskData.entityType}: ${taskData.entityId}`);
        }
        taskData.entityId = entity.id;
      }

      // Create task object
      const task = TaskModel.create(taskData);
      
      // Save task to Firestore
      const collectionPath = TaskModel.getCollectionPath(farmId);
      const createdTask = await firestoreService.createDocument(collectionPath, task);

      // Get assignee name for response
      const assigneeName = await this.getAssigneeName(assigneeId, farmId);
      
      return buildResponse.taskResponse({
        ...createdTask,
        assignedToName: assigneeName
      }, 'assigned');

    } catch (error) {
      console.error('AssignTask Error:', error);
      return buildResponse.error('Failed to assign task');
    }
  }

  /**
   * Extract task data from intent
   * @param {object} intentData - Intent data from LLM
   * @param {string} assignedBy - User ID of task creator
   * @returns {object} Task data
   */
  extractTaskData(intentData, assignedBy) {
    return {
      title: intentData.title || '',
      description: intentData.description || '',
      assignedTo: intentData.assignedTo || '',
      assignedBy: assignedBy,
      zoneId: intentData.zoneId || null,
      entityId: intentData.entityId || null,
      entityType: intentData.entityType || null,
      priority: intentData.priority || 'medium',
      dueDate: intentData.dueDate || null,
      notes: intentData.notes || ''
    };
  }

  /**
   * Resolve assignee name to user ID
   * @param {string} assignee - Assignee name or ID
   * @param {string} farmId - Farm ID
   * @returns {string|null} User ID
   */
  async resolveAssignee(assignee, farmId) {
    try {
      // First check if it's already a valid user ID
      const userById = await firestoreService.getDocument(`farms/${farmId}/users`, assignee);
      if (userById) {
        return assignee;
      }

      // Search by name
      const users = await firestoreService.getDocuments(`farms/${farmId}/users`);
      const userByName = users.find(user => 
        user.name && user.name.toLowerCase() === assignee.toLowerCase()
      );

      return userByName ? userByName.id : null;

    } catch (error) {
      console.error('Error resolving assignee:', error);
      return null;
    }
  }

  /**
   * Resolve zone name to zone ID
   * @param {string} zoneName - Zone name or ID
   * @param {string} farmId - Farm ID
   * @returns {object|null} Zone object
   */
  async resolveZone(zoneName, farmId) {
    try {
      // First check if it's already a valid zone ID
      const zoneById = await firestoreService.getDocument(`farms/${farmId}/zones`, zoneName);
      if (zoneById) {
        return zoneById;
      }

      // Search by name
      const zones = await firestoreService.getDocuments(`farms/${farmId}/zones`);
      const zoneByName = zones.find(zone => 
        zone.name && zone.name.toLowerCase().includes(zoneName.toLowerCase())
      );

      return zoneByName || null;

    } catch (error) {
      console.error('Error resolving zone:', error);
      return null;
    }
  }

  /**
   * Resolve entity name/tag to entity ID
   * @param {string} entityIdentifier - Entity name, tag, or ID
   * @param {string} entityType - Type of entity
   * @param {string} farmId - Farm ID
   * @returns {object|null} Entity object
   */
  async resolveEntity(entityIdentifier, entityType, farmId) {
    try {
      const collectionPath = `farms/${farmId}/${entityType}`;
      
      // First check if it's already a valid entity ID
      const entityById = await firestoreService.getDocument(collectionPath, entityIdentifier);
      if (entityById) {
        return entityById;
      }

      // Search by tag ID (for animals)
      if (entityType === 'animals') {
        const entities = await firestoreService.getDocuments(collectionPath, {
          tagId: entityIdentifier
        });
        if (entities.length > 0) {
          return entities[0];
        }
      }

      // Search by name
      const entities = await firestoreService.getDocuments(collectionPath);
      const entityByName = entities.find(entity => 
        entity.name && entity.name.toLowerCase().includes(entityIdentifier.toLowerCase())
      );

      return entityByName || null;

    } catch (error) {
      console.error('Error resolving entity:', error);
      return null;
    }
  }

  /**
   * Get assignee name for response
   * @param {string} userId - User ID
   * @param {string} farmId - Farm ID
   * @returns {string} User name
   */
  async getAssigneeName(userId, farmId) {
    try {
      const user = await firestoreService.getDocument(`farms/${farmId}/users`, userId);
      return user ? user.name : userId;
    } catch (error) {
      console.error('Error getting assignee name:', error);
      return userId;
    }
  }

  /**
   * Check if user can assign tasks
   * @param {string} userId - User ID
   * @param {string} farmId - Farm ID
   * @returns {boolean} Can assign tasks
   */
  async canAssignTasks(userId, farmId) {
    try {
      const user = await firestoreService.getDocument(`farms/${farmId}/users`, userId);
      return user && (user.role === 'admin' || user.role === 'owner');
    } catch (error) {
      console.error('Error checking task assignment permissions:', error);
      return false;
    }
  }

  /**
   * Get available assignees for a farm
   * @param {string} farmId - Farm ID
   * @returns {Array} List of available assignees
   */
  async getAvailableAssignees(farmId) {
    try {
      const users = await firestoreService.getDocuments(`farms/${farmId}/users`, {
        status: 'active'
      });
      
      return users.map(user => ({
        id: user.id,
        name: user.name,
        role: user.role
      }));
      
    } catch (error) {
      console.error('Error getting available assignees:', error);
      return [];
    }
  }

  /**
   * Create recurring task
   * @param {object} taskData - Task data
   * @param {string} farmId - Farm ID
   * @returns {Array} Created recurring tasks
   */
  async createRecurringTask(taskData, farmId) {
    try {
      const tasks = [];
      const collectionPath = TaskModel.getCollectionPath(farmId);
      
      // Create base task
      const baseTask = TaskModel.create(taskData);
      const createdTask = await firestoreService.createDocument(collectionPath, baseTask);
      tasks.push(createdTask);

      // Create future instances based on recurring pattern
      if (taskData.recurring && taskData.recurringPattern) {
        const futureInstances = this.generateRecurringInstances(taskData, 4); // Next 4 instances
        
        for (const instance of futureInstances) {
          const recurringTask = TaskModel.create(instance);
          const created = await firestoreService.createDocument(collectionPath, recurringTask);
          tasks.push(created);
        }
      }

      return tasks;
      
    } catch (error) {
      console.error('Error creating recurring task:', error);
      throw error;
    }
  }

  /**
   * Generate recurring task instances
   * @param {object} taskData - Base task data
   * @param {number} count - Number of instances to create
   * @returns {Array} Recurring task instances
   */
  generateRecurringInstances(taskData, count) {
    const instances = [];
    const baseDate = new Date(taskData.dueDate || new Date());
    
    for (let i = 1; i <= count; i++) {
      const instanceDate = new Date(baseDate);
      
      switch (taskData.recurringPattern) {
        case 'daily':
          instanceDate.setDate(baseDate.getDate() + i);
          break;
        case 'weekly':
          instanceDate.setDate(baseDate.getDate() + (i * 7));
          break;
        case 'monthly':
          instanceDate.setMonth(baseDate.getMonth() + i);
          break;
      }
      
      instances.push({
        ...taskData,
        dueDate: instanceDate.toISOString().split('T')[0],
        title: `${taskData.title} (${i + 1})`
      });
    }
    
    return instances;
  }
}

module.exports = new AssignTaskController();
