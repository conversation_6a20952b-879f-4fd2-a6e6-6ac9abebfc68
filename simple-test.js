// Simple test to check OpenRouter integration
const admin = require('firebase-admin');

// Initialize Firebase Admin for local testing
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: 'kissandost-9570f'
  });
}

// Set environment variables
process.env.OPENROUTER_API_KEY = '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';
process.env.OPENROUTER_MODEL = 'anthropic/claude-3.5-sonnet';

async function testOpenRouter() {
  console.log('🧪 Testing OpenRouter Integration...\n');

  try {
    const OpenRouter = require('./functions/mcp/openRouter');
    
    console.log('📡 Sending request to OpenRouter...');
    console.log('🔑 API Key:', process.env.OPENROUTER_API_KEY ? 'Set' : 'Not set');
    
    const intentData = await OpenRouter.parseIntent('Show all animals', {
      userId: 'test_user_123',
      farmId: 'test_farm_456'
    });
    
    console.log('✅ OpenRouter Response:');
    console.log(JSON.stringify(intentData, null, 2));
    
  } catch (error) {
    console.log('❌ OpenRouter Error:', error.message);
    console.log('📄 Stack:', error.stack);
  }
}

async function testFirestore() {
  console.log('\n💾 Testing Firestore Service...\n');

  try {
    const firestoreService = require('./functions/mcp/services/firestoreService');
    
    console.log('📊 Attempting to read from Firestore...');
    
    // Try to get documents (will be empty but should not error)
    const animals = await firestoreService.getDocuments('farms/test_farm_456/animals');
    console.log('✅ Firestore Service working!');
    console.log(`📊 Found ${animals.length} animals`);
    
  } catch (error) {
    console.log('❌ Firestore Service Error:', error.message);
    console.log('📄 Stack:', error.stack);
  }
}

async function testBuildPrompt() {
  console.log('\n🔧 Testing BuildPrompt Utility...\n');

  try {
    const BuildPrompt = require('./functions/mcp/utils/buildPrompt');
    
    const prompt = BuildPrompt.createIntentPrompt('Show all animals', {
      userId: 'test_user_123',
      farmId: 'test_farm_456'
    });
    
    console.log('✅ BuildPrompt working!');
    console.log('📋 System Prompt Length:', prompt.systemPrompt.length);
    console.log('📋 User Prompt:', prompt.userPrompt);
    
  } catch (error) {
    console.log('❌ BuildPrompt Error:', error.message);
    console.log('📄 Stack:', error.stack);
  }
}

async function runTests() {
  await testBuildPrompt();
  await testFirestore();
  await testOpenRouter();
  console.log('\n🏁 Tests completed!');
}

runTests().catch(console.error);
