// Standalone test for MCP functionality without Firebase emulators
const express = require('express');
const admin = require('firebase-admin');

// Initialize Firebase Admin for local testing
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: 'demo-project'
  });
}

// Import MCP handler
const mcpHandler = require('./functions/mcp');

const app = express();
app.use(express.json());

// Add MCP endpoint
app.post('/mcp', mcpHandler);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'MCP Server is running' });
});

const PORT = 3000;

app.listen(PORT, () => {
  console.log(`🚀 MCP Server running on http://localhost:${PORT}`);
  console.log(`📋 Test endpoint: POST http://localhost:${PORT}/mcp`);
  console.log(`🔍 Health check: GET http://localhost:${PORT}/health`);
});

// Test cases
const testCases = [
  {
    name: 'Simple Health Check',
    request: {
      text: 'hello',
      userId: 'test_user_123',
      farmId: 'test_farm_456'
    }
  },
  {
    name: 'List Animals',
    request: {
      text: 'Show all animals',
      userId: 'test_user_123',
      farmId: 'test_farm_456'
    }
  },
  {
    name: 'Assign Task',
    request: {
      text: 'Assign watering task to John for tomorrow',
      userId: 'test_user_123',
      farmId: 'test_farm_456'
    }
  }
];

// Auto-run tests after server starts
setTimeout(async () => {
  console.log('\n🧪 Running automated tests...\n');
  
  const axios = require('axios');
  const baseUrl = `http://localhost:${PORT}`;
  
  for (const testCase of testCases) {
    try {
      console.log(`📋 Testing: ${testCase.name}`);
      console.log(`📤 Request: "${testCase.request.text}"`);
      
      const response = await axios.post(`${baseUrl}/mcp`, testCase.request);
      
      console.log(`✅ Status: ${response.data.status}`);
      console.log(`💬 Message: ${response.data.message}`);
      
      if (response.data.data) {
        console.log(`📊 Data:`, JSON.stringify(response.data.data, null, 2));
      }
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      if (error.response) {
        console.log(`📄 Response:`, error.response.data);
      }
    }
    
    console.log('---\n');
  }
  
}, 2000);

module.exports = app;
