// Test the exact MCP endpoint issue
const axios = require('axios');
const admin = require('firebase-admin');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: 'kissandost-9570f' // Your actual Firebase project ID
  });
}

const db = admin.firestore();

// Your actual data
const FARM_ID = 'KWct4gdzPvEJfS4XP35C';
const USER_ID = 'rir9S0uQlhRVp9cmhLJZXT54Atc2';
const MCP_URL = 'http://localhost:5001/demo-project/us-central1/mcp';

async function setupTestData() {
  console.log('🌱 Setting up test data...\n');
  
  try {
    // Create farm if it doesn't exist
    const farmDoc = await db.collection('farms').doc(FARM_ID).get();
    if (!farmDoc.exists) {
      await db.collection('farms').doc(FARM_ID).set({
        name: 'Test Farm',
        owner: USER_ID,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      console.log('✅ Farm created');
    }

    // Create users
    await db.collection('farms').doc(FARM_ID).collection('users').doc(USER_ID).set({
      name: 'Test Admin',
      role: 'admin',
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    await db.collection('farms').doc(FARM_ID).collection('users').doc('user_adnan').set({
      name: 'Adnan',
      role: 'worker',
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Create zones
    await db.collection('farms').doc(FARM_ID).collection('zones').doc('zone_3').set({
      name: 'Zone 3',
      type: 'field',
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Create animals
    await db.collection('farms').doc(FARM_ID).collection('animals').doc('animal_1').set({
      tagId: 'BUFF-001',
      name: 'Bella',
      typeId: 'buffalo',
      status: 'active',
      healthStatus: 'healthy',
      zoneId: 'zone_3',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    await db.collection('farms').doc(FARM_ID).collection('animals').doc('animal_2').set({
      tagId: 'COW-001',
      name: 'Moti',
      typeId: 'cow',
      status: 'active',
      healthStatus: 'healthy',
      zoneId: 'zone_3',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Create plants
    await db.collection('farms').doc(FARM_ID).collection('plants').doc('plant_1').set({
      name: 'Tomato Crop',
      varietyId: 'tomato',
      status: 'growing',
      healthStatus: 'healthy',
      zoneId: 'zone_3',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    await db.collection('farms').doc(FARM_ID).collection('plants').doc('plant_2').set({
      name: 'Wheat Field',
      varietyId: 'wheat',
      status: 'growing',
      healthStatus: 'healthy',
      zoneId: 'zone_3',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Create lookups
    const lookups = [
      { id: 'buffalo', title: 'Buffalo', categoryName: 'Animal Type', type: 'animal' },
      { id: 'cow', title: 'Cow', categoryName: 'Animal Type', type: 'animal' },
      { id: 'tomato', title: 'Tomato', categoryName: 'Plant Variety', type: 'plant' },
      { id: 'wheat', title: 'Wheat', categoryName: 'Plant Variety', type: 'plant' }
    ];

    for (const lookup of lookups) {
      await db.collection('lookups').doc(lookup.id).set({
        title: lookup.title,
        categoryName: lookup.categoryName,
        type: lookup.type,
        parentId: null,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
    }

    console.log('✅ Test data setup complete');
    
    // Verify data was created
    const animalsSnapshot = await db.collection(`farms/${FARM_ID}/animals`).get();
    const plantsSnapshot = await db.collection(`farms/${FARM_ID}/plants`).get();
    const usersSnapshot = await db.collection(`farms/${FARM_ID}/users`).get();
    
    console.log(`📊 Created: ${animalsSnapshot.size} animals, ${plantsSnapshot.size} plants, ${usersSnapshot.size} users`);
    
  } catch (error) {
    console.error('❌ Error setting up test data:', error);
  }
}

async function testMCPEndpoint() {
  console.log('\n🧪 Testing MCP Endpoint...\n');
  
  const testCases = [
    {
      name: 'List Animals',
      request: {
        text: 'Show all animals',
        userId: USER_ID,
        farmId: FARM_ID
      }
    },
    {
      name: 'List Plants',
      request: {
        text: 'Show all plants',
        userId: USER_ID,
        farmId: FARM_ID
      }
    },
    {
      name: 'Assign Task (Today)',
      request: {
        text: 'Assign watering task to Adnan in Zone 3 for today',
        userId: USER_ID,
        farmId: FARM_ID
      }
    },
    {
      name: 'Assign Task (Tomorrow)',
      request: {
        text: 'Assign feeding task to Adnan for tomorrow',
        userId: USER_ID,
        farmId: FARM_ID
      }
    }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`📋 Testing: ${testCase.name}`);
      console.log(`📤 Request: "${testCase.request.text}"`);
      
      const response = await axios.post(MCP_URL, testCase.request, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 15000
      });

      console.log(`✅ Status: ${response.data.status}`);
      console.log(`💬 Message: ${response.data.message}`);
      
      if (response.data.data) {
        console.log(`📊 Data:`, JSON.stringify(response.data.data, null, 2));
      }
      
      if (response.data.meta) {
        console.log(`📋 Meta:`, JSON.stringify(response.data.meta, null, 2));
      }
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      
      if (error.response) {
        console.log(`📄 Response Status: ${error.response.status}`);
        console.log(`📄 Response Data:`, error.response.data);
      }
      
      if (error.code === 'ECONNREFUSED') {
        console.log('💡 Make sure Firebase emulators are running: firebase emulators:start');
      }
    }
    
    console.log('---\n');
  }
}

async function verifyDataDirectly() {
  console.log('🔍 Verifying data directly from Firestore...\n');
  
  try {
    // Check animals
    const animalsPath = `farms/${FARM_ID}/animals`;
    const animalsSnapshot = await db.collection(animalsPath).get();
    console.log(`🐄 Animals in ${animalsPath}: ${animalsSnapshot.size}`);
    animalsSnapshot.forEach(doc => {
      console.log(`  - ${doc.id}:`, doc.data());
    });

    // Check plants
    const plantsPath = `farms/${FARM_ID}/plants`;
    const plantsSnapshot = await db.collection(plantsPath).get();
    console.log(`🌱 Plants in ${plantsPath}: ${plantsSnapshot.size}`);
    plantsSnapshot.forEach(doc => {
      console.log(`  - ${doc.id}:`, doc.data());
    });

    // Check users
    const usersPath = `farms/${FARM_ID}/users`;
    const usersSnapshot = await db.collection(usersPath).get();
    console.log(`👥 Users in ${usersPath}: ${usersSnapshot.size}`);
    usersSnapshot.forEach(doc => {
      console.log(`  - ${doc.id}:`, doc.data());
    });

    // Test firestoreService directly
    console.log('\n🔧 Testing firestoreService directly...');
    const firestoreService = require('./functions/mcp/services/firestoreService');
    
    const animals = await firestoreService.getDocuments(animalsPath);
    console.log(`✅ firestoreService returned ${animals.length} animals`);
    
    const plants = await firestoreService.getDocuments(plantsPath);
    console.log(`✅ firestoreService returned ${plants.length} plants`);
    
  } catch (error) {
    console.error('❌ Error verifying data:', error);
  }
}

async function runFullTest() {
  console.log('🚀 Starting Full MCP Test...\n');
  
  await setupTestData();
  await verifyDataDirectly();
  await testMCPEndpoint();
  
  console.log('🏁 Full test completed!');
}

runFullTest().catch(console.error);
