const openRouter = require('./openRouter');
const buildResponse = require('./utils/buildResponse');

// Import controllers
const listEntitiesController = require('./controllers/listEntities');
const assignTaskController = require('./controllers/assignTask');
const completeTaskController = require('./controllers/completeTask');
const createRecordController = require('./controllers/createRecord');

/**
 * Main router that processes user requests
 * 1. Sends text to OpenRouter LLM for intent parsing
 * 2. Routes parsed intent to appropriate controller
 * 3. Returns formatted response
 */
class Router {
  async processRequest({ text, userId, farmId }) {
    try {
      // Step 1: Parse intent using OpenRouter LLM
      const intentData = await openRouter.parseIntent(text, { userId, farmId });

      if (!intentData || !intentData.intent) {
        return buildResponse('error', 'Could not understand the request', null);
      }

      // Step 2: Route to appropriate controller based on intent
      let result;
      
      switch (intentData.intent) {
        case 'listEntities':
          result = await listEntitiesController.handle(intentData, { userId, farmId });
          break;
          
        case 'assignTask':
          result = await assignTaskController.handle(intentData, { userId, farmId });
          break;
          
        case 'completeTask':
          result = await completeTaskController.handle(intentData, { userId, farmId });
          break;
          
        case 'createRecord':
          result = await createRecordController.handle(intentData, { userId, farmId });
          break;
          
        default:
          return buildResponse('error', `Unknown intent: ${intentData.intent}`, null);
      }

      return result;

    } catch (error) {
      console.error('Router Error:', error);
      return buildResponse('error', 'Failed to process request', null);
    }
  }
}

module.exports = new Router();
