// Simple data seeder for MCP testing
const admin = require('firebase-admin');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: 'kissandost-9570f'
  });
}

const db = admin.firestore();

async function seedData() {
  console.log('🌱 Seeding test data for MCP server...');
  
  const farmId = 'test_farm_456';
  const userId = 'test_user_123';
  
  try {
    // 1. Create Farm
    console.log('📋 Creating farm...');
    await db.collection('farms').doc(farmId).set({
      name: 'Green Valley Test Farm',
      owner: userId,
      location: 'Test Location',
      area: 50,
      areaUnit: 'acres',
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // 2. Create Users
    console.log('👥 Creating users...');
    await db.collection('farms').doc(farmId).collection('users').doc(userId).set({
      name: 'Test Admin',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    await db.collection('farms').doc(farmId).collection('users').doc('user_john').set({
      name: 'John <PERSON>',
      email: '<EMAIL>',
      role: 'worker',
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    await db.collection('farms').doc(farmId).collection('users').doc('user_sarah').set({
      name: 'Sarah Caretaker',
      email: '<EMAIL>',
      role: 'caretaker',
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // 3. Create Zones
    console.log('🏞️ Creating zones...');
    await db.collection('farms').doc(farmId).collection('zones').doc('zone_1').set({
      name: 'Zone 1',
      type: 'field',
      area: 10,
      areaUnit: 'acres',
      status: 'active',
      description: 'Main field area',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    await db.collection('farms').doc(farmId).collection('zones').doc('zone_2').set({
      name: 'Zone 2',
      type: 'garden',
      area: 5,
      areaUnit: 'acres',
      status: 'active',
      description: 'Garden area',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    await db.collection('farms').doc(farmId).collection('zones').doc('barn_1').set({
      name: 'Main Barn',
      type: 'barn',
      capacity: 50,
      currentOccupancy: 3,
      status: 'active',
      description: 'Main animal barn',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // 4. Create Animals
    console.log('🐄 Creating animals...');
    await db.collection('farms').doc(farmId).collection('animals').doc('animal_buff_91').set({
      tagId: 'BUFF-91',
      name: 'Bella',
      typeId: 'lookup_buffalo',
      gender: 'female',
      birthDate: '2020-03-15',
      weight: 450,
      color: 'black',
      zoneId: 'barn_1',
      status: 'active',
      healthStatus: 'healthy',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    await db.collection('farms').doc(farmId).collection('animals').doc('animal_cow_23').set({
      tagId: 'COW-23',
      name: 'Moti',
      typeId: 'lookup_cow',
      gender: 'male',
      birthDate: '2019-08-10',
      weight: 380,
      color: 'brown',
      zoneId: 'barn_1',
      status: 'active',
      healthStatus: 'healthy',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    await db.collection('farms').doc(farmId).collection('animals').doc('animal_goat_45').set({
      tagId: 'GOAT-45',
      name: 'Choti',
      typeId: 'lookup_goat',
      gender: 'female',
      birthDate: '2021-06-20',
      weight: 45,
      color: 'white',
      zoneId: 'barn_1',
      status: 'active',
      healthStatus: 'healthy',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // 5. Create Plants
    console.log('🌱 Creating plants...');
    await db.collection('farms').doc(farmId).collection('plants').doc('plant_tomato_1').set({
      name: 'Tomato Crop 1',
      varietyId: 'lookup_tomato',
      cropTypeId: 'lookup_vegetable',
      zoneId: 'zone_1',
      plantedDate: '2025-06-01',
      expectedHarvestDate: '2025-09-01',
      quantity: 1000,
      unit: 'plants',
      status: 'growing',
      healthStatus: 'healthy',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    await db.collection('farms').doc(farmId).collection('plants').doc('plant_wheat_2').set({
      name: 'Wheat Field 2',
      varietyId: 'lookup_wheat',
      cropTypeId: 'lookup_grain',
      zoneId: 'zone_2',
      plantedDate: '2025-05-15',
      expectedHarvestDate: '2025-10-15',
      quantity: 5,
      unit: 'acres',
      status: 'growing',
      healthStatus: 'healthy',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // 6. Create Lookups
    console.log('📚 Creating lookup data...');
    const lookups = [
      { id: 'lookup_buffalo', title: 'Buffalo', categoryName: 'Animal Type', type: 'animal' },
      { id: 'lookup_cow', title: 'Cow', categoryName: 'Animal Type', type: 'animal' },
      { id: 'lookup_goat', title: 'Goat', categoryName: 'Animal Type', type: 'animal' },
      { id: 'lookup_tomato', title: 'Roma Tomato', categoryName: 'Plant Variety', type: 'plant' },
      { id: 'lookup_wheat', title: 'Punjab Wheat', categoryName: 'Plant Variety', type: 'plant' },
      { id: 'lookup_vegetable', title: 'Vegetable', categoryName: 'Crop Type', type: 'plant' },
      { id: 'lookup_grain', title: 'Grain', categoryName: 'Crop Type', type: 'plant' }
    ];

    for (const lookup of lookups) {
      await db.collection('lookups').doc(lookup.id).set({
        title: lookup.title,
        categoryName: lookup.categoryName,
        type: lookup.type,
        parentId: null,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
    }

    // 7. Create Sample Tasks
    console.log('📋 Creating sample tasks...');
    await db.collection('farms').doc(farmId).collection('tasks').doc('task_feeding').set({
      title: 'Morning Feeding',
      description: 'Feed all animals in the main barn',
      assignedTo: 'user_john',
      assignedBy: userId,
      zoneId: 'barn_1',
      priority: 'high',
      status: 'pending',
      dueDate: '2025-07-31',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    await db.collection('farms').doc(farmId).collection('tasks').doc('task_watering').set({
      title: 'Water Tomato Plants',
      description: 'Water the tomato plants in Zone 1',
      assignedTo: 'user_sarah',
      assignedBy: userId,
      zoneId: 'zone_1',
      entityId: 'plant_tomato_1',
      entityType: 'plants',
      priority: 'medium',
      status: 'pending',
      dueDate: '2025-07-30',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    console.log('✅ Test data seeded successfully!');
    console.log('\n📊 Summary:');
    console.log('- Farm: test_farm_456');
    console.log('- Users: 3 (admin, worker, caretaker)');
    console.log('- Zones: 3 (field, garden, barn)');
    console.log('- Animals: 3 (buffalo, cow, goat)');
    console.log('- Plants: 2 (tomato, wheat)');
    console.log('- Lookups: 7 (animal types, plant varieties)');
    console.log('- Tasks: 2 (feeding, watering)');
    console.log('\n🧪 Ready for testing!');

  } catch (error) {
    console.error('❌ Error seeding data:', error);
  }
}

seedData().then(() => {
  console.log('\n🏁 Seeding completed!');
  process.exit(0);
}).catch(console.error);
