// Complete test for MCP Server functionality
const axios = require('axios');
const admin = require('firebase-admin');

// Initialize Firebase Admin for local testing
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: 'demo-project'
  });
}

const db = admin.firestore();

// Test configuration
const BASE_URL = 'http://localhost:5001/demo-project/us-central1/mcp';
const TEST_DATA = {
  userId: 'test_user_123',
  farmId: 'test_farm_456'
};

// Sample data for testing
async function seedTestData() {
  console.log('🌱 Seeding test data...');
  
  const farmId = TEST_DATA.farmId;
  
  try {
    // Create farm
    await db.collection('farms').doc(farmId).set({
      name: 'Test Farm',
      owner: TEST_DATA.userId,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Create users
    await db.collection('farms').doc(farmId).collection('users').doc(TEST_DATA.userId).set({
      name: 'Test Admin',
      role: 'admin',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    await db.collection('farms').doc(farmId).collection('users').doc('user_john').set({
      name: 'John Worker',
      role: 'worker',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Create zones
    await db.collection('farms').doc(farmId).collection('zones').doc('zone_1').set({
      name: 'Zone 1',
      type: 'field',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Create animals
    await db.collection('farms').doc(farmId).collection('animals').doc('animal_1').set({
      tagId: 'BUFF-91',
      name: 'Bella',
      typeId: 'buffalo',
      status: 'active',
      healthStatus: 'healthy',
      zoneId: 'zone_1',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Create plants
    await db.collection('farms').doc(farmId).collection('plants').doc('plant_1').set({
      name: 'Tomato Crop',
      varietyId: 'tomato',
      status: 'growing',
      healthStatus: 'healthy',
      zoneId: 'zone_1',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Create lookups
    await db.collection('lookups').doc('buffalo').set({
      title: 'Buffalo',
      categoryName: 'Animal Type',
      type: 'animal',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    console.log('✅ Test data seeded successfully!');
    
  } catch (error) {
    console.log('❌ Error seeding data:', error.message);
  }
}

// Test cases
const testCases = [
  {
    name: 'List Animals',
    request: {
      text: 'Show all animals',
      ...TEST_DATA
    }
  },
  {
    name: 'List Animals by Zone',
    request: {
      text: 'Show animals in Zone 1',
      ...TEST_DATA
    }
  },
  {
    name: 'List Plants',
    request: {
      text: 'Show all plants',
      ...TEST_DATA
    }
  },
  {
    name: 'Assign Task',
    request: {
      text: 'Assign feeding task to John for tomorrow',
      ...TEST_DATA
    }
  },
  {
    name: 'Create Health Check',
    request: {
      text: 'Create health check for animal BUFF-91 with good condition',
      ...TEST_DATA
    }
  }
];

async function runTests() {
  console.log('🧪 Starting MCP Server Tests...\n');

  // First seed the data
  await seedTestData();
  console.log('');

  for (const testCase of testCases) {
    try {
      console.log(`📋 Testing: ${testCase.name}`);
      console.log(`📤 Request: "${testCase.request.text}"`);
      
      const response = await axios.post(BASE_URL, testCase.request, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 15000
      });

      console.log(`✅ Status: ${response.data.status}`);
      console.log(`💬 Message: ${response.data.message}`);
      
      if (response.data.data) {
        console.log(`📊 Data:`, JSON.stringify(response.data.data, null, 2));
      }
      
      if (response.data.meta) {
        console.log(`📋 Meta:`, JSON.stringify(response.data.meta, null, 2));
      }
      
      console.log('---\n');

    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      
      if (error.response) {
        console.log(`📄 Response:`, error.response.data);
      }
      
      console.log('---\n');
    }
  }

  console.log('🏁 Tests completed!');
}

// Health check function
async function healthCheck() {
  try {
    console.log('🔍 Checking MCP Server health...');
    
    const response = await axios.post(BASE_URL, {
      text: 'hello',
      ...TEST_DATA
    }, {
      timeout: 5000
    });

    console.log('✅ Server is running!');
    console.log('Response:', response.data);
    
  } catch (error) {
    console.log('❌ Server is not responding');
    console.log('Error:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure to start the Firebase emulators first:');
      console.log('   firebase emulators:start');
    }
  }
}

// Command line interface
const command = process.argv[2];

switch (command) {
  case 'health':
    healthCheck();
    break;
  case 'seed':
    seedTestData();
    break;
  case 'test':
  default:
    runTests();
    break;
}

module.exports = {
  runTests,
  healthCheck,
  seedTestData,
  testCases
};
