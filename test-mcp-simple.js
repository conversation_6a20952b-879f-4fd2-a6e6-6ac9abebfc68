// Simple MCP test with proper emulator connection
const axios = require('axios');

const MCP_URL = 'http://localhost:5001/kissandost-9570f/us-central1/mcp';
const FARM_ID = 'KWct4gdzPvEJfS4XP35C';
const USER_ID = 'rir9S0uQlhRVp9cmhLJZXT54Atc2';

async function testMCP() {
  console.log('🧪 Testing MCP with Emulator Data...\n');
  
  const tests = [
    {
      name: 'List Animals',
      request: {
        text: 'Show all animals',
        userId: USER_ID,
        farmId: FARM_ID
      }
    },
    {
      name: 'List Plants',
      request: {
        text: 'Show all plants',
        userId: USER_ID,
        farmId: FARM_ID
      }
    },
    {
      name: 'Assign Task (Tomorrow)',
      request: {
        text: 'Assign watering task to <PERSON>nan for tomorrow',
        userId: USER_ID,
        farmId: FARM_ID
      }
    }
  ];

  for (const test of tests) {
    try {
      console.log(`📋 Testing: ${test.name}`);
      console.log(`📤 Request: "${test.request.text}"`);
      
      const response = await axios.post(MCP_URL, test.request, {
        headers: { 'Content-Type': 'application/json' },
        timeout: 15000
      });
      
      console.log(`✅ Status: ${response.data.status}`);
      console.log(`💬 Message: ${response.data.message}`);
      
      if (response.data.data) {
        if (Array.isArray(response.data.data)) {
          console.log(`📊 Found ${response.data.data.length} items`);
          response.data.data.forEach((item, index) => {
            console.log(`  ${index + 1}. ${item.name || item.title || item.tagId || 'Unknown'}`);
          });
        } else {
          console.log(`📊 Data:`, response.data.data);
        }
      }
      
      if (response.data.meta) {
        console.log(`📋 Meta:`, response.data.meta);
      }
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      if (error.response) {
        console.log(`📄 Response:`, error.response.data);
      }
    }
    
    console.log('---\n');
  }
}

testMCP().catch(console.error);
