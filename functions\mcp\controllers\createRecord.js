const firestoreService = require('../services/firestoreService');
const lookupService = require('../services/lookupService');
const buildResponse = require('../utils/buildResponse');

/**
 * Controller for creating sub-entity records
 * Handles creation of health checks, feeding logs, pregnancy records, etc.
 */
class CreateRecordController {
  /**
   * Handle record creation request
   * @param {object} intentData - Parsed intent data from LLM
   * @param {object} context - User and farm context
   * @returns {object} Response with record creation result
   */
  async handle(intentData, context) {
    try {
      const { userId, farmId } = context;
      const { entityType, entityId, tagId, recordType, data } = intentData;

      // Find the target entity
      const entity = await this.findEntity(entityType, entityId, tagId, farmId);
      if (!entity) {
        return buildResponse.notFound(`${entityType} entity`);
      }

      // Validate record type for entity type
      if (!this.isValidRecordType(entityType, recordType)) {
        return buildResponse.error(`Invalid record type '${recordType}' for ${entityType}`);
      }

      // Create record based on type
      const record = await this.createRecord(entity, recordType, data, userId, farmId);
      
      // Get entity name for response
      const entityName = this.getEntityName(entity, entityType);

      return buildResponse.recordResponse(record, recordType, entityName);

    } catch (error) {
      console.error('CreateRecord Error:', error);
      return buildResponse.error('Failed to create record');
    }
  }

  /**
   * Find entity by ID or tag
   * @param {string} entityType - Type of entity
   * @param {string} entityId - Entity ID
   * @param {string} tagId - Tag ID (for animals)
   * @param {string} farmId - Farm ID
   * @returns {object|null} Entity object
   */
  async findEntity(entityType, entityId, tagId, farmId) {
    try {
      const collectionPath = `farms/${farmId}/${entityType}`;

      // Try to find by entity ID first
      if (entityId) {
        const entity = await firestoreService.getDocument(collectionPath, entityId);
        if (entity) {
          return entity;
        }
      }

      // Try to find by tag ID (for animals)
      if (tagId && entityType === 'animals') {
        const entities = await firestoreService.getDocuments(collectionPath, {
          tagId: tagId
        });
        if (entities.length > 0) {
          return entities[0];
        }
      }

      return null;

    } catch (error) {
      console.error('Error finding entity:', error);
      return null;
    }
  }

  /**
   * Check if record type is valid for entity type
   * @param {string} entityType - Type of entity
   * @param {string} recordType - Type of record
   * @returns {boolean} Is valid
   */
  isValidRecordType(entityType, recordType) {
    const validRecords = {
      'animals': ['healthCheck', 'pregnancy', 'feedingLog', 'weightRecord', 'vaccination', 'breeding'],
      'plants': ['harvestRecord', 'wateringLog', 'fertilizingLog', 'pestTreatment', 'growthRecord']
    };

    return validRecords[entityType] && validRecords[entityType].includes(recordType);
  }

  /**
   * Create record based on type
   * @param {object} entity - Target entity
   * @param {string} recordType - Type of record
   * @param {object} data - Record data
   * @param {string} userId - User ID
   * @param {string} farmId - Farm ID
   * @returns {object} Created record
   */
  async createRecord(entity, recordType, data, userId, farmId) {
    const entityType = entity.tagId ? 'animals' : 'plants'; // Simple detection
    const parentPath = `farms/${farmId}/${entityType}/${entity.id}`;
    
    switch (recordType) {
      case 'healthCheck':
        return await this.createHealthCheck(parentPath, data, userId);
      case 'pregnancy':
        return await this.createPregnancyRecord(parentPath, data, userId);
      case 'feedingLog':
        return await this.createFeedingLog(parentPath, data, userId);
      case 'weightRecord':
        return await this.createWeightRecord(parentPath, data, userId);
      case 'vaccination':
        return await this.createVaccinationRecord(parentPath, data, userId);
      case 'breeding':
        return await this.createBreedingRecord(parentPath, data, userId);
      case 'harvestRecord':
        return await this.createHarvestRecord(parentPath, data, userId);
      case 'wateringLog':
        return await this.createWateringLog(parentPath, data, userId);
      case 'fertilizingLog':
        return await this.createFertilizingLog(parentPath, data, userId);
      case 'pestTreatment':
        return await this.createPestTreatment(parentPath, data, userId);
      case 'growthRecord':
        return await this.createGrowthRecord(parentPath, data, userId);
      default:
        throw new Error(`Unknown record type: ${recordType}`);
    }
  }

  /**
   * Create health check record
   * @param {string} parentPath - Parent entity path
   * @param {object} data - Health check data
   * @param {string} userId - User ID
   * @returns {object} Created record
   */
  async createHealthCheck(parentPath, data, userId) {
    const healthCheck = {
      condition: data.condition || 'healthy',
      weight: data.weight || null,
      temperature: data.temperature || null,
      heartRate: data.heartRate || null,
      symptoms: data.symptoms || [],
      treatment: data.treatment || '',
      veterinarian: data.veterinarian || '',
      notes: data.notes || '',
      date: data.date || new Date().toISOString().split('T')[0],
      recordedBy: userId,
      followUpRequired: data.followUpRequired || false,
      followUpDate: data.followUpDate || null
    };

    return await firestoreService.createSubcollectionDocument(
      parentPath, 'healthChecks', healthCheck
    );
  }

  /**
   * Create pregnancy record
   * @param {string} parentPath - Parent entity path
   * @param {object} data - Pregnancy data
   * @param {string} userId - User ID
   * @returns {object} Created record
   */
  async createPregnancyRecord(parentPath, data, userId) {
    const pregnancy = {
      matingDate: data.matingDate || null,
      expectedDueDate: data.expectedDueDate || null,
      actualDueDate: data.actualDueDate || null,
      maleParentId: data.maleParentId || null,
      status: data.status || 'confirmed', // confirmed, delivered, failed
      numberOfOffspring: data.numberOfOffspring || null,
      complications: data.complications || '',
      veterinarian: data.veterinarian || '',
      notes: data.notes || '',
      recordedBy: userId
    };

    return await firestoreService.createSubcollectionDocument(
      parentPath, 'pregnancies', pregnancy
    );
  }

  /**
   * Create feeding log
   * @param {string} parentPath - Parent entity path
   * @param {object} data - Feeding data
   * @param {string} userId - User ID
   * @returns {object} Created record
   */
  async createFeedingLog(parentPath, data, userId) {
    const feeding = {
      feedType: data.feedType || '',
      quantity: data.quantity || 0,
      unit: data.unit || 'kg',
      time: data.time || new Date().toISOString(),
      cost: data.cost || null,
      supplier: data.supplier || '',
      notes: data.notes || '',
      recordedBy: userId
    };

    return await firestoreService.createSubcollectionDocument(
      parentPath, 'feedingLogs', feeding
    );
  }

  /**
   * Create weight record
   * @param {string} parentPath - Parent entity path
   * @param {object} data - Weight data
   * @param {string} userId - User ID
   * @returns {object} Created record
   */
  async createWeightRecord(parentPath, data, userId) {
    const weight = {
      weight: data.weight || 0,
      unit: data.unit || 'kg',
      date: data.date || new Date().toISOString().split('T')[0],
      method: data.method || 'scale', // scale, estimation
      notes: data.notes || '',
      recordedBy: userId
    };

    return await firestoreService.createSubcollectionDocument(
      parentPath, 'weightRecords', weight
    );
  }

  /**
   * Create vaccination record
   * @param {string} parentPath - Parent entity path
   * @param {object} data - Vaccination data
   * @param {string} userId - User ID
   * @returns {object} Created record
   */
  async createVaccinationRecord(parentPath, data, userId) {
    const vaccination = {
      vaccineName: data.vaccineName || '',
      vaccineType: data.vaccineType || '',
      dose: data.dose || '',
      administrationDate: data.administrationDate || new Date().toISOString().split('T')[0],
      nextDueDate: data.nextDueDate || null,
      veterinarian: data.veterinarian || '',
      batchNumber: data.batchNumber || '',
      manufacturer: data.manufacturer || '',
      sideEffects: data.sideEffects || '',
      notes: data.notes || '',
      recordedBy: userId
    };

    return await firestoreService.createSubcollectionDocument(
      parentPath, 'vaccinations', vaccination
    );
  }

  /**
   * Create breeding record
   * @param {string} parentPath - Parent entity path
   * @param {object} data - Breeding data
   * @param {string} userId - User ID
   * @returns {object} Created record
   */
  async createBreedingRecord(parentPath, data, userId) {
    const breeding = {
      maleParentId: data.maleParentId || null,
      breedingDate: data.breedingDate || new Date().toISOString().split('T')[0],
      method: data.method || 'natural', // natural, artificial
      success: data.success || null,
      notes: data.notes || '',
      recordedBy: userId
    };

    return await firestoreService.createSubcollectionDocument(
      parentPath, 'breedingRecords', breeding
    );
  }

  /**
   * Create harvest record
   * @param {string} parentPath - Parent entity path
   * @param {object} data - Harvest data
   * @param {string} userId - User ID
   * @returns {object} Created record
   */
  async createHarvestRecord(parentPath, data, userId) {
    const harvest = {
      harvestDate: data.harvestDate || new Date().toISOString().split('T')[0],
      quantity: data.quantity || 0,
      unit: data.unit || 'kg',
      quality: data.quality || 'good', // excellent, good, fair, poor
      marketPrice: data.marketPrice || null,
      soldPrice: data.soldPrice || null,
      buyer: data.buyer || '',
      storageLocation: data.storageLocation || '',
      notes: data.notes || '',
      recordedBy: userId
    };

    return await firestoreService.createSubcollectionDocument(
      parentPath, 'harvestRecords', harvest
    );
  }

  /**
   * Create watering log
   * @param {string} parentPath - Parent entity path
   * @param {object} data - Watering data
   * @param {string} userId - User ID
   * @returns {object} Created record
   */
  async createWateringLog(parentPath, data, userId) {
    const watering = {
      date: data.date || new Date().toISOString().split('T')[0],
      duration: data.duration || null, // in minutes
      method: data.method || 'manual', // manual, sprinkler, drip
      waterSource: data.waterSource || '',
      notes: data.notes || '',
      recordedBy: userId
    };

    return await firestoreService.createSubcollectionDocument(
      parentPath, 'wateringLogs', watering
    );
  }

  /**
   * Create fertilizing log
   * @param {string} parentPath - Parent entity path
   * @param {object} data - Fertilizing data
   * @param {string} userId - User ID
   * @returns {object} Created record
   */
  async createFertilizingLog(parentPath, data, userId) {
    const fertilizing = {
      date: data.date || new Date().toISOString().split('T')[0],
      fertilizerType: data.fertilizerType || '',
      quantity: data.quantity || 0,
      unit: data.unit || 'kg',
      method: data.method || 'broadcast', // broadcast, side-dress, foliar
      cost: data.cost || null,
      supplier: data.supplier || '',
      notes: data.notes || '',
      recordedBy: userId
    };

    return await firestoreService.createSubcollectionDocument(
      parentPath, 'fertilizingLogs', fertilizing
    );
  }

  /**
   * Create pest treatment record
   * @param {string} parentPath - Parent entity path
   * @param {object} data - Treatment data
   * @param {string} userId - User ID
   * @returns {object} Created record
   */
  async createPestTreatment(parentPath, data, userId) {
    const treatment = {
      date: data.date || new Date().toISOString().split('T')[0],
      pestType: data.pestType || '',
      treatmentType: data.treatmentType || '', // chemical, organic, biological
      product: data.product || '',
      quantity: data.quantity || 0,
      unit: data.unit || 'ml',
      method: data.method || 'spray',
      effectiveness: data.effectiveness || null, // 1-10 scale
      cost: data.cost || null,
      notes: data.notes || '',
      recordedBy: userId
    };

    return await firestoreService.createSubcollectionDocument(
      parentPath, 'pestTreatments', treatment
    );
  }

  /**
   * Create growth record
   * @param {string} parentPath - Parent entity path
   * @param {object} data - Growth data
   * @param {string} userId - User ID
   * @returns {object} Created record
   */
  async createGrowthRecord(parentPath, data, userId) {
    const growth = {
      date: data.date || new Date().toISOString().split('T')[0],
      height: data.height || null,
      width: data.width || null,
      stage: data.stage || '', // seedling, vegetative, flowering, fruiting
      healthStatus: data.healthStatus || 'healthy',
      notes: data.notes || '',
      images: data.images || [],
      recordedBy: userId
    };

    return await firestoreService.createSubcollectionDocument(
      parentPath, 'growthRecords', growth
    );
  }

  /**
   * Get entity name for response
   * @param {object} entity - Entity object
   * @param {string} entityType - Entity type
   * @returns {string} Entity name
   */
  getEntityName(entity, entityType) {
    if (entityType === 'animals' && entity.tagId) {
      return entity.name ? `${entity.name} (${entity.tagId})` : entity.tagId;
    }
    return entity.name || entity.id;
  }
}

module.exports = new CreateRecordController();
