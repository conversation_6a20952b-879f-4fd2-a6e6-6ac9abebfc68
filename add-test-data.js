// Add test data to your existing farm
const admin = require('firebase-admin');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: 'kissandost-9570f' // Your actual Firebase project ID
  });
}

const db = admin.firestore();

async function addTestData() {
  console.log('🌱 Adding test data to your farm...\n');
  
  const farmId = 'KWct4gdzPvEJfS4XP35C'; // Your actual farm ID
  
  try {
    // 1. Add user "Adnan"
    console.log('👤 Adding user Adnan...');
    await db.collection('farms').doc(farmId).collection('users').doc('user_adnan').set({
      name: 'Adnan',
      email: '<EMAIL>',
      role: 'worker',
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });
    console.log('✅ User Adnan added');

    // 2. Add Zone 3
    console.log('🏞️ Adding Zone 3...');
    await db.collection('farms').doc(farmId).collection('zones').doc('zone_3').set({
      name: 'Zone 3',
      type: 'field',
      area: 8,
      areaUnit: 'acres',
      status: 'active',
      description: 'Field area for crops',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });
    console.log('✅ Zone 3 added');

    // 3. Add some sample animals if none exist
    console.log('🐄 Adding sample animals...');
    
    // Check if animals exist first
    const animalsSnapshot = await db.collection('farms').doc(farmId).collection('animals').get();
    
    if (animalsSnapshot.empty) {
      console.log('No animals found, adding sample animals...');
      
      await db.collection('farms').doc(farmId).collection('animals').doc('animal_buffalo_1').set({
        tagId: 'BUFF-001',
        name: 'Bella',
        typeId: 'buffalo',
        gender: 'female',
        birthDate: '2020-03-15',
        weight: 450,
        color: 'black',
        zoneId: 'zone_3',
        status: 'active',
        healthStatus: 'healthy',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      await db.collection('farms').doc(farmId).collection('animals').doc('animal_cow_1').set({
        tagId: 'COW-001',
        name: 'Moti',
        typeId: 'cow',
        gender: 'male',
        birthDate: '2019-08-10',
        weight: 380,
        color: 'brown',
        zoneId: 'zone_3',
        status: 'active',
        healthStatus: 'healthy',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      console.log('✅ Sample animals added');
    } else {
      console.log(`✅ Found ${animalsSnapshot.size} existing animals`);
    }

    // 4. Add some sample plants if none exist
    console.log('🌱 Adding sample plants...');
    
    const plantsSnapshot = await db.collection('farms').doc(farmId).collection('plants').get();
    
    if (plantsSnapshot.empty) {
      console.log('No plants found, adding sample plants...');
      
      await db.collection('farms').doc(farmId).collection('plants').doc('plant_tomato_1').set({
        name: 'Tomato Crop 1',
        varietyId: 'tomato',
        cropTypeId: 'vegetable',
        zoneId: 'zone_3',
        plantedDate: '2025-06-01',
        expectedHarvestDate: '2025-09-01',
        quantity: 500,
        unit: 'plants',
        status: 'growing',
        healthStatus: 'healthy',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      await db.collection('farms').doc(farmId).collection('plants').doc('plant_wheat_1').set({
        name: 'Wheat Field 1',
        varietyId: 'wheat',
        cropTypeId: 'grain',
        zoneId: 'zone_3',
        plantedDate: '2025-05-15',
        expectedHarvestDate: '2025-10-15',
        quantity: 3,
        unit: 'acres',
        status: 'growing',
        healthStatus: 'healthy',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      console.log('✅ Sample plants added');
    } else {
      console.log(`✅ Found ${plantsSnapshot.size} existing plants`);
    }

    // 5. Add some basic lookups
    console.log('📚 Adding lookup data...');
    
    const lookups = [
      { id: 'buffalo', title: 'Buffalo', categoryName: 'Animal Type', type: 'animal' },
      { id: 'cow', title: 'Cow', categoryName: 'Animal Type', type: 'animal' },
      { id: 'goat', title: 'Goat', categoryName: 'Animal Type', type: 'animal' },
      { id: 'tomato', title: 'Tomato', categoryName: 'Plant Variety', type: 'plant' },
      { id: 'wheat', title: 'Wheat', categoryName: 'Plant Variety', type: 'plant' },
      { id: 'vegetable', title: 'Vegetable', categoryName: 'Crop Type', type: 'plant' },
      { id: 'grain', title: 'Grain', categoryName: 'Crop Type', type: 'plant' }
    ];

    for (const lookup of lookups) {
      await db.collection('lookups').doc(lookup.id).set({
        title: lookup.title,
        categoryName: lookup.categoryName,
        type: lookup.type,
        parentId: null,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
    }
    console.log('✅ Lookup data added');

    console.log('\n🎉 Test data added successfully!');
    console.log('\n📊 Summary:');
    console.log('- ✅ User "Adnan" added');
    console.log('- ✅ Zone 3 added');
    console.log('- ✅ Sample animals added (if none existed)');
    console.log('- ✅ Sample plants added (if none existed)');
    console.log('- ✅ Lookup data added');
    
    console.log('\n🧪 Now you can test:');
    console.log('1. "Show all animals" - should show animals');
    console.log('2. "Show all plants" - should show plants');
    console.log('3. "Assign watering task to Adnan in Zone 3 for today" - should work');

  } catch (error) {
    console.error('❌ Error adding test data:', error);
  }
}

addTestData().then(() => {
  console.log('\n🏁 Setup completed!');
  process.exit(0);
}).catch(console.error);
