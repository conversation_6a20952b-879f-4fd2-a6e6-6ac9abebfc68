// Quick test to verify everything is working
const admin = require('firebase-admin');
const axios = require('axios');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: 'kissandost-9570f'
  });
}

const db = admin.firestore();

// Your actual data
const FARM_ID = 'KWct4gdzPvEJfS4XP35C';
const USER_ID = 'rir9S0uQlhRVp9cmhLJZXT54Atc2';
const MCP_URL = 'http://localhost:5001/kissandost-9570f/us-central1/mcp';

async function quickTest() {
  console.log('🚀 Quick MCP Test\n');
  
  try {
    // Step 1: Add some test data
    console.log('1️⃣ Adding test data...');
    
    // Add farm
    await db.collection('farms').doc(FARM_ID).set({
      name: 'Test Farm',
      owner: USER_ID,
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    // Add user
    await db.collection('farms').doc(FARM_ID).collection('users').doc(USER_ID).set({
      name: 'Test Admin',
      role: 'admin',
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    // Add Adnan
    await db.collection('farms').doc(FARM_ID).collection('users').doc('user_adnan').set({
      name: 'Adnan',
      role: 'worker',
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    // Add Zone 3
    await db.collection('farms').doc(FARM_ID).collection('zones').doc('zone_3').set({
      name: 'Zone 3',
      type: 'field',
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    // Add animals
    await db.collection('farms').doc(FARM_ID).collection('animals').doc('animal_1').set({
      tagId: 'BUFF-001',
      name: 'Bella',
      typeId: 'buffalo',
      status: 'active',
      healthStatus: 'healthy',
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    // Add plants
    await db.collection('farms').doc(FARM_ID).collection('plants').doc('plant_1').set({
      name: 'Tomato Crop',
      varietyId: 'tomato',
      status: 'growing',
      healthStatus: 'healthy',
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    console.log('✅ Test data added');
    
    // Step 2: Verify data exists
    console.log('\n2️⃣ Verifying data...');
    const animalsSnapshot = await db.collection(`farms/${FARM_ID}/animals`).get();
    const plantsSnapshot = await db.collection(`farms/${FARM_ID}/plants`).get();
    console.log(`✅ Found ${animalsSnapshot.size} animals, ${plantsSnapshot.size} plants`);
    
    // Step 3: Test MCP endpoint
    console.log('\n3️⃣ Testing MCP endpoint...');
    
    const testRequests = [
      {
        name: 'List Animals',
        data: {
          text: 'Show all animals',
          userId: USER_ID,
          farmId: FARM_ID
        }
      },
      {
        name: 'List Plants',
        data: {
          text: 'Show all plants',
          userId: USER_ID,
          farmId: FARM_ID
        }
      },
      {
        name: 'Assign Task',
        data: {
          text: 'Assign watering task to Adnan in Zone 3 for tomorrow',
          userId: USER_ID,
          farmId: FARM_ID
        }
      }
    ];
    
    for (const test of testRequests) {
      try {
        console.log(`\n📋 Testing: ${test.name}`);
        console.log(`📤 Request: "${test.data.text}"`);
        
        const response = await axios.post(MCP_URL, test.data, {
          headers: { 'Content-Type': 'application/json' },
          timeout: 10000
        });
        
        console.log(`✅ Status: ${response.data.status}`);
        console.log(`💬 Message: ${response.data.message}`);
        
        if (response.data.data) {
          if (Array.isArray(response.data.data)) {
            console.log(`📊 Found ${response.data.data.length} items`);
            if (response.data.data.length > 0) {
              console.log(`📋 Sample:`, response.data.data[0]);
            }
          } else {
            console.log(`📊 Data:`, response.data.data);
          }
        }
        
      } catch (error) {
        console.log(`❌ Error: ${error.message}`);
        if (error.response) {
          console.log(`📄 Response:`, error.response.data);
        }
      }
    }
    
    console.log('\n🎉 Quick test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

quickTest().then(() => {
  process.exit(0);
}).catch(console.error);
