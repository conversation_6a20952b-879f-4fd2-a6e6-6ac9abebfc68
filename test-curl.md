# MCP Server Testing with cURL

## Prerequisites
1. Firebase emulators running: `firebase emulators:start`
2. MCP function deployed to emulator

## Test Commands

### 1. Health Check / Simple Request
```bash
curl -X POST http://localhost:5001/kissandost-9570f/us-central1/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "text": "hello",
    "userId": "test_user_123",
    "farmId": "test_farm_456"
  }'
```

### 2. List Animals
```bash
curl -X POST http://localhost:5001/kissandost-9570f/us-central1/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Show all animals",
    "userId": "test_user_123",
    "farmId": "test_farm_456"
  }'
```

### 3. Assign Task
```bash
curl -X POST http://localhost:5001/kissandost-9570f/us-central1/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Assign watering task to <PERSON><PERSON> for tomorrow",
    "userId": "rir9S0uQlhRVp9cmhLJZXT54Atc2",
    "farmId": "KWct4gdzPvEJfS4XP35C"
  }'
```

### 4. Create Health Check
```bash
curl -X POST http://localhost:5001/kissandost-9570f/us-central1/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Create health check for animal BUFF-91 with good condition",
    "userId": "rir9S0uQlhRVp9cmhLJZXT54Atc2",
    "farmId": "KWct4gdzPvEJfS4XP35C"
  }'
```

### 5. Add New Animal
```bash
curl -X POST http://localhost:5001/kissandost-9570f/us-central1/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Add new buffalo named Moti with tag COW-123 in Zone 1",
    "userId": "rir9S0uQlhRVp9cmhLJZXT54Atc2",
    "farmId": "KWct4gdzPvEJfS4XP35C"
  }'
```

### 6. Add New Plant
```bash
curl -X POST http://localhost:5001/kissandost-9570f/us-central1/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Add tomato plants in Zone 3 with 500 plants",
    "userId": "rir9S0uQlhRVp9cmhLJZXT54Atc2",
    "farmId": "KWct4gdzPvEJfS4XP35C"
  }'
```

## Expected Response Format
```json
{
  "status": "success|error|warning",
  "message": "Human readable message",
  "data": {
    // Response data
  },
  "timestamp": "2025-07-30T11:37:00.000Z"
}
```

## Troubleshooting

### If you get connection refused:
- Make sure Firebase emulators are running
- Check the correct project ID in the URL
- Verify the function is deployed

### If you get function errors:
- Check Firebase Functions logs
- Verify environment variables are set
- Check OpenRouter API key is valid

### If you get Firestore errors:
- Make sure Firestore emulator is running
- Check firestore.rules file exists
- Verify project ID matches in all configs
