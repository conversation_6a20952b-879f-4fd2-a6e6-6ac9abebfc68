const admin = require('firebase-admin');
const router = require('./router');
const buildResponse = require('./utils/buildResponse');

/**
 * Main MCP handler function
 * Processes incoming requests and routes them to appropriate controllers
 */
async function mcpHandler(req, res) {
  // Set CORS headers
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.set('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).send('');
    return;
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json(buildResponse('error', 'Method not allowed', null));
  }

  try {
    // Validate request body
    const { text, userId, farmId } = req.body;

    if (!text || !userId || !farmId) {
      return res.status(400).json(buildResponse('error', 'Missing required fields: text, userId, farmId', null));
    }

    // Process the request through the router
    const result = await router.processRequest({
      text,
      userId,
      farmId
    });

    // Return the result
    res.status(200).json(result);

  } catch (error) {
    console.error('MCP Handler Error:', error);
    res.status(500).json(buildResponse('error', 'Internal server error', null));
  }
}

module.exports = mcpHandler;
